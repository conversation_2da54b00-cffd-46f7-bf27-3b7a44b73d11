import json
from flask import request, Blueprint
from api.config.json_response import JsonResponse
from api.config.config import *

pc_packets = Blueprint('pc_packets', __name__)


# 套餐列表列表--分页
@pc_packets.route("/packets/list", methods=["POST"])
def get_packet_list():
    params = json.loads(request.data)
    db = SQLManager()
    sql = "select * from tbl_packets where is_delete=0 "
    sql_count = "select count(id) as total from tbl_packets where is_delete=0 "
    count_res = db.get_list(sql_count)
    total = count_res[0]['total']
    limit = params['pageSize']
    current = params['pageNum']
    limit_min = (int(current) - 1) * int(limit)
    sql += ' limit ' + str(limit_min) + ',' + str(limit)
    res_list = db.get_list(sql)
    res = {
        "list": res_list,
        "pageNum": current,
        "pageSize": limit,
        "total": total
    }
    return JsonResponse.success(msg='success', data=res)


# 添加套餐
@pc_packets.route("/packets/add", methods=["POST"])
def add_packets():
    params = json.loads(request.data)
    db = SQLManager()
    packets_name = params["packets_name"]
    check_repeat = db.get_list('select id from tbl_packets where packets_name=%s and is_delete=0', args=[params['packets_name']])
    if len(check_repeat) >= 1:
        return JsonResponse.success(msg='error', data='套餐名称已存在')
    else:
        try:
            unit_price = params["unit_price"]
            lifespan = params["lifespan"]
            # 添加数据到套餐表
            insert_packets = 'insert into tbl_packets (packets_name, unit_price, lifespan) values(%s, %s, %s)'
            db.cursor.execute(insert_packets, args=[packets_name, unit_price, lifespan])
            db.conn.commit()
        except Exception as err:
            print('添加套餐失败', err)
            db.conn.rollback()
            return JsonResponse.fail(msg='error', data=err)
        finally:
            db.close()
        return JsonResponse.success(msg='success', data='添加成功')


# 修改套餐
@pc_packets.route("/packets/update", methods=["POST"])
def update_packets():
    params = json.loads(request.data)
    db = SQLManager()
    try:
        packets_name = params["packets_name"]
        unit_price = params["unit_price"]
        lifespan = params["lifespan"]
        # 添加数据到套餐表
        update_packets = 'update set tbl_packets packets_name=%s, unit_price=%s, lifespan=%s'
        db.cursor.execute(update_packets, args=[packets_name, unit_price, lifespan])
        db.conn.commit()
    except Exception as err:
        print('修改套餐失败', err)
        db.conn.rollback()
        return JsonResponse.fail(msg='error', data=err)
    finally:
        db.close()
    return JsonResponse.success(msg='success', data='更新成功')


# 禁用套餐
@pc_packets.route("/packets/disabled", methods=["POST"])
def disabled_packets():
    params = json.loads(request.data)
    print(params)
    print(params["id"])
    if "id" in params and params["id"] is not None and params["id"] != "":
        db = SQLManager()
        try:
            # 删除套餐表
            delete_packets_sql = 'update tbl_packets set is_disabled=1 where id=%s'
            db.cursor.execute(delete_packets_sql, args=[params['id']])
            db.conn.commit()
        except Exception as err:
            print('禁用套餐失败', err)
            db.conn.rollback()
            return JsonResponse.fail(msg='error', data=err)
        finally:
            db.close()
        return JsonResponse.success(msg='success', data='禁用成功')
    else:
        return JsonResponse.fail(msg='error', data="缺少参数")


# 启用套餐
@pc_packets.route("/packets/enable", methods=["POST"])
def enable_packets():
    params = json.loads(request.data)
    if "id" in params and params["id"] is not None and params["id"] != "":
        db = SQLManager()
        try:
            # 删除套餐表
            delete_packets_sql = 'update tbl_packets set is_disabled=0 where id=%s'
            db.cursor.execute(delete_packets_sql, args=[params['id']])
            db.conn.commit()
        except Exception as err:
            print('启用套餐失败', err)
            db.conn.rollback()
            return JsonResponse.fail(msg='error', data=err)
        finally:
            db.close()
        return JsonResponse.success(msg='success', data='启用成功')
    else:
        return JsonResponse.fail(msg='error', data="缺少参数")