import random
import time
import hashlib
import requests
from api.config.public import generate_random_str


class getTokenController:
    # 生成随机字符串
    @staticmethod
    def getToken(self, UID, SID):
        UID = UID
        SID = SID
        randomStr = generate_random_str(6)
        timestamp = int(round(time.time() * 1000))
        tempStr = UID + SID + randomStr + str(timestamp)
        signatureMd5 = hashlib.md5(tempStr.encode('utf-8')).hexdigest()
        signature = signatureMd5.upper()
        postData = {
            'uid': UID,
            'sid': SID,
            'random': randomStr,
            'timestamp': timestamp,
            'signature': signature,
        }
        res = requests.post('http:/yun.oxdtu.com/api/v1/token/initToken', params=postData)
        res = res.json()
        token = res['data']['token']
        return token
