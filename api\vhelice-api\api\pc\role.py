import json
from flask import request, Blueprint
from api.config.json_response import JsonResponse
from api.config.config import *
import datetime as datetime

pc_role = Blueprint('pc_role', __name__)


# 角色列表--无分页
@pc_role.route("/role/list", methods=["POST"])
def get_role_list():
    db = SQLManager()
    sql = "select r.*, GROUP_CONCAT(rm.menu_id SEPARATOR ',') as permission from sys_role r LEFT JOIN sys_role_menu rm on rm.role_id=r.id where r.is_delete=0 group by r.id"
    resList = db.get_list(sql)
    db.close()
    return JsonResponse.success(msg='success', data=resList)


# 角色列表-含分页
@pc_role.route("/role/list/page", methods=["POST"])
def get_role_list_page():
    data = json.loads(request.data)
    db = SQLManager()
    sql = "select r.*, GROUP_CONCAT(rm.menu_id SEPARATOR ',') as permission from sys_role r LEFT JOIN sys_role_menu rm on rm.role_id=r.id where r.is_delete=0 group by r.id"
    sql_count = "select count(id) as total from sys_role where is_delete=0 "
    countRes = db.get_list(sql_count)
    total = countRes[0]['total']
    limit = data['pageSize']
    current = data['pageNum']
    min = (int(current) - 1) * int(limit)
    sql += ' limit ' + str(min) + ',' + str(limit)
    resList = db.get_list(sql)
    res = {
        "list": resList,
        "pageNum": current,
        "pageSize": limit,
        "total": total
    }
    return JsonResponse.success(msg='success', data=res)


# 添加角色
@pc_role.route("/role/add", methods=["POST"])
def add_role():
    data = json.loads(request.data)
    db = SQLManager()
    check_repeat = db.get_list('select id from sys_role where role_name=%s and is_delete=0', args=[data['role_name']])
    if len(check_repeat) >= 1:
        return JsonResponse.fail(msg='error', data='角色名称已存在')
    else:
        try:
            # 添加角色时, 将关联的菜单保存到数据库
            # 添加数据到角色表
            db.cursor.execute('insert into sys_role (role_name, description) values(%s, %s)', args=[data['role_name'], data['description']])
            role_id = db.conn.fetchone()[0]
            permission_arr = []
            for i in data['permission']:
                temp_json = (role_id, i)
                permission_arr.append(temp_json)
            db.cursor.executemany('insert into sys_role_menu (role_id, menu_id) values(%s, %s)', args=permission_arr)
            db.conn.commit()
        except Exception as err:
            print('添加角色失败', err)
            db.conn.rollback()
            return JsonResponse.fail(msg='error', data=err)
        finally:
            db.close()
        return JsonResponse.success(msg='success', data='添加成功')


# 更新角色
@pc_role.route("/role/update", methods=["POST"])
def update_role():
    data = json.loads(request.data)
    db = SQLManager()
    check_repeat = db.get_list('select id from sys_role where role_name=%s and is_delete=0 and id!=%s', args=[data['role_name'], data['id']])
    if len(check_repeat) >= 1:
        return JsonResponse.fail(msg="error", data='角色名称已存在')
    else:
        try:
            # 添加角色时, 先删除原本绑定的菜单数据 , 然后再重新添加
            db.cursor.execute('update sys_role set role_name=%s, description=%s where id=%s', args=[data['role_name'], data['description'], data['id']])
            # 删除原有绑定的角色菜单数据
            db.cursor.execute('delete from sys_role_menu where role_id=%s', args=[data['id']])
            # 重新添加
            permission_arr = []
            for i in data['permission']:
                temp_json = (data['id'], i)
                permission_arr.append(temp_json)
            db.cursor.executemany('insert into sys_role_menu (role_id, menu_id) values(%s, %s)',
                                  args=permission_arr)
            db.conn.commit()
        except Exception as err:
            print('更新角色失败', err)
            db.conn.rollback()
            return JsonResponse.fail(msg='error', data=err)
        finally:
            db.close()
        return JsonResponse.success(msg='success', data='更新成功')


# 删除角色
@pc_role.route("/role/delete", methods=["POST"])
def delete_role():
    data = json.loads(request.data)
    db = SQLManager()
    try:
        # 删除角色数据
        db.cursor.execute(
            sql='update sys_role set is_delete=1 where id=%s',
            args=[data['id']])
        # 删除原有绑定的角色菜单数据
        db.cursor.execute('delete from sys_role_menu where role_id=%s', args=[data['id']])
        db.conn.commit()
    except Exception as err:
        print('删除角色失败', err)
        db.conn.rollback()
        return JsonResponse.fail(msg='error', data=err)
    finally:
        db.close()
    return JsonResponse.success(msg='success', data='删除成功')


# 导出角色列表
@pc_role.route("/role/export", methods=["POST"])
def export_role():
    db = SQLManager()
    data = json.loads(request.data)
    limit = data['pageSize']
    current = data['pageNum']
    min = (int(current) - 1) * int(limit)
    selectSql = "select * from sys_role where is_delete=0 "
    sql_count = "select count(id) as total from sys_role where is_delete=0 "
    countRes = db.get_list(sql_count)
    total = countRes[0]['total']
    selectSql += ' limit ' + str(min) + ',' + str(limit)
    resList = db.get_list(selectSql)
    tempList = [
        ['用户名称', '角色描述', '创建时间']
    ]
    for item in resList:
        arr = [
            item['role_name'],
            item['description'],
            datetime.datetime.strftime(item['create_time'], '%Y-%m-%d %H:%M:%S')
        ]
        tempList.append(arr)
    db.close()
    return JsonResponse.success(msg="success", data=tempList)
