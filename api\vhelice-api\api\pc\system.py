import json
from flask import request, Blueprint
from api.config.json_response import JsonResponse
from api.config.config import *

pc_system = Blueprint('pc_system', __name__)


# 获取所有的菜单列表
@pc_system.route("/menu/list", methods=["post"])
def get_menu_list():
    sql = "select * from sys_menu order by sort asc "
    db = SQLManager()
    resMenuList = db.get_list(sql)
    db.close()
    return JsonResponse.success(msg='success', data=resMenuList)


# 添加菜单
@pc_system.route("/menu/add", methods=["post"])
def add_menu():
    data = json.loads(request.data)
    db = SQLManager()
    # 判断当前序号是否被占用
    checkSoltSql = "select id from sys_menu where parent_id=%s and sort=%s and is_delete=0"
    checkRes = db.get_one(checkSoltSql, args=[data['parent_id'], data['sort']])
    if checkRes is None:
        try:
            sql = "insert into sys_menu (parent_id,path,type,component,permission,title,icon,sort,isFull,isHide,isAffix,isKeepAlive) values(%s,%s,%s,%s,%s,%s,%s,%s,%s,%s,%s,%s)"
            db.cursor.execute(sql, args=[data['parent_id'], data['path'], data['type'], data['component'], data['permission'], data['title'], data['icon'],
                                         data['sort'], data['isFull'], data['isHide'], data['isAffix'], data['isKeepAlive']])
            db.conn.commit()
        except Exception as err:
            db.conn.rollback()
            return JsonResponse.fail(msg='error', data=err)
        finally:
            db.close()
        return JsonResponse.success(msg='success', data="添加成功")
    else:
        return JsonResponse.fail(msg='error', data="序号被占用")


# 修改菜单
@pc_system.route("/menu/update", methods=["post"])
def update_menu():
    data = json.loads(request.data)
    db = SQLManager()
    try:
        # 判断当前序号是否被占用
        checkSoltSql = "select id from sys_menu where sort=%s and id !=%s and parent_id=%s"
        checkRes = db.get_one(checkSoltSql, args=[data['sort'], data['id'], data['parent_id']])
        if checkRes is None:
            sql = "update sys_menu set parent_id=%s,path=%s,type=%s,component=%s,permission=%s,title=%s,icon=%s,sort=%s,isFull=%s,isHide=%s," \
                  "isAffix=%s, isKeepAlive=%s where id=%s"
            db.modify(sql, args=[data['parent_id'], data['path'], data['type'], data['component'], data['permission'], data['title'], data['icon'],
                                 data['sort'], data['isFull'], data['isHide'], data['isAffix'], data['isKeepAlive'], data['id']])
        else:
            return JsonResponse.fail(msg='error', data="当前序号已被占用")
    except Exception as err:
        print("修改菜单错误", err)
    finally:
        db.close()
    return JsonResponse.success(msg='success', data="修改成功")


# 禁用菜单
@pc_system.route("/menu/disable", methods=["post"])
def disable_menu():
    data = json.loads(request.data)
    db = SQLManager()
    try:
        sql = "update sys_menu set is_delete=1 where id=%s"
        db.modify(sql, args=[data['id']])
    except Exception as err:
        print("禁用菜单错误", err)
    finally:
        db.close()
    return JsonResponse.success(msg='success', data="禁用成功")


# 启用菜单
@pc_system.route("/menu/enable", methods=["post"])
def enable_menu():
    data = json.loads(request.data)
    db = SQLManager()
    try:
        sql = "update sys_menu set is_delete=0 where id=%s"
        db.modify(sql, args=[data['id']])
    except Exception as err:
        print("启用菜单错误", err)
    finally:
        db.close()
    return JsonResponse.success(msg='success', data="启用成功")


# 根据用户角色获取菜单列表
@pc_system.route("/menu/role/list", methods=["post"])
def get_menu_role():
    # 根据当前登录账户判断是否有对应的权限
    data = json.loads(request.data)
    db = SQLManager()
    resMenuList = db.get_list("select m.* from sys_menu m inner join sys_role_menu rm on rm.menu_id= m.id where rm.role_id=%s", args=[data['role_id']])
    db.close()
    resList = []
    for parent in resMenuList:
        if parent['type'] != 3 and parent["parent_id"] == 0:
            parentJson = {
                "path": parent["path"],
                "name": parent["name"],
                "meta": {
                    "icon": parent["icon"],
                    "title": parent["title"],
                    "sort": parent["sort"],
                    "isLink": "",
                    "isHide": False if parent["isHide"] == 0 else True,
                    "isFull": False if parent["isFull"] == 0 else True,
                    "isAffix": False if parent["isAffix"] == 0 else True,
                    "isKeepAlive": False if parent["isKeepAlive"] == 0 else True,
                    "permission": parent["permission"],
                },
                "children": []
            }
            if parent["type"] == 1:
                parentJson['redirect'] = parent["component"]
            else:
                parentJson['component'] = parent["component"]
            for child in resMenuList:
                if parent["id"] == child["parent_id"] and parent['type'] != 3:
                    childJson = {
                        "path": child["path"],
                        "name": child["name"],
                        "component": child["component"],
                        "meta": {
                            "icon": child["icon"],
                            "title": child["title"],
                            "isLink": "",
                            "sort": child["sort"],
                            "isHide": False if child["isHide"] == 0 else True,
                            "isFull": False if child["isFull"] == 0 else True,
                            "isAffix": False if child["isAffix"] == 0 else True,
                            "isKeepAlive": False if child["isKeepAlive"] == 0 else True,
                            "permission": child["permission"],
                        },
                        "children": []
                    }
                    for grandson in resMenuList:
                        if child["id"] == grandson["parent_id"] and grandson['type'] != 3:
                            grandsonJson = {
                                "path": grandson["path"],
                                "name": grandson["name"],
                                "component": grandson["component"],
                                "meta": {
                                    "icon": grandson["icon"],
                                    "title": grandson["title"],
                                    "isLink": "",
                                    "sort": grandson["sort"],
                                    "isHide": False if grandson["isHide"] == 0 else True,
                                    "isFull": False if grandson["isFull"] == 0 else True,
                                    "isAffix": False if grandson["isAffix"] == 0 else True,
                                    "isKeepAlive": False if grandson["isKeepAlive"] == 0 else True,
                                    "permission": grandson["permission"],
                                },
                            }
                            childJson["children"].append(grandsonJson)
                    parentJson["children"].append(childJson)

            if len(parentJson['children']) <= 0:
                del parentJson['children']
            resList.append(parentJson)
    return JsonResponse.success(msg='success', data=resList)


# 获取用户页面按钮权限
@pc_system.route("/system/role/buttons", methods=["post"])
def get_role_buttons():
    data = json.loads(request.data)
    sql = "select m.permission from sys_menu m inner join sys_role_menu rm on rm.menu_id= m.id where rm.role_id=%s and m.type=3"
    db = SQLManager()
    buttonList = db.get_list(sql, args=[data['role_id']])
    # 直接转换成数组格式输出
    res = []
    for i in buttonList:
        res.append(i['permission'])
    db.close()
    return JsonResponse.success(msg='success', data=res)
