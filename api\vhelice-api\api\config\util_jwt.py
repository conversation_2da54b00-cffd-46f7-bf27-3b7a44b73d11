import datetime
import jwt


class Util_jwt:
    def __init__(self):
        self.secret = 'ou_xin'

    # 加密token
    def encryption_token(self, user_id, user_name):
        # 定义一个要加密的字典
        time = datetime.datetime.utcnow()
        payload = {
            "user_id": user_id,
            "user_name": user_name,
            "iat": time,
            'exp': time + datetime.timedelta(milliseconds=60000)}
        # 定义加密的密钥
        token = jwt.encode(payload, self.secret, algorithm="HS256")
        return token

    # 解密token
    def decrypt_token(self, token):
        payload = None
        msg = None
        try:
            payload = jwt.decode(token, self.secret, algorithms=['HS256'])
            print(payload)
        except jwt.exceptions.DecodeError:
            msg = '认证失败'
        except jwt.exceptions.ExpiredSignatureError:
            msg = '签名过期'
        except jwt.exceptions.InvalidAlgorithmError:
            msg = '无效算法错误'
        if payload:
            return payload
        else:
            return msg
