import json
import os
import re
import zipfile
from io import BytesIO

import qrcode
import datetime as datetime
import requests
from flask import request, Blueprint, send_file
from api.config.json_response import JsonResponse
from api.config.config import *
from api.miniprogram.gps import wgs84_to_gcj02

pc_device = Blueprint('pc_device', __name__)


# 设备相关---device

# 设备自动添加默认参数
# 中控板: 车辆警戒, 车辆解锁, 静音警戒, 车辆启动, 车辆开尾门, 开窗, 电量低, 震动报警, 钥匙报警
# GPS模组: 警戒, 解锁, 静音警戒, 启动, 开尾门, 查询


# 添加设备时自动创建二维码文件
def create_qrcode_img(imei_code, company_id):
    qrcode_data = "https://mradmin.oxdtu.com/?code=" + str(imei_code)
    try:
        save_dir = "../background/files/qrcode/" + str(company_id)
        if not os.path.exists(save_dir):
            os.makedirs(save_dir)
        # 创建二维码
        qr = qrcode.QRCode(
            version=1,
            error_correction=qrcode.constants.ERROR_CORRECT_L,
            box_size=10,
            border=4,
        )
        filename = str(imei_code) + ".png"
        filepath = os.path.join(save_dir, filename)
        qr.add_data(qrcode_data)
        qr.make(fit=True)
        img = qr.make_image(fill='black', back_color='white')
        img.save(filepath)
    except Exception as error:
        print("创建失败", error)


# 判断字符串是否为纯数字
def is_number(s):
    return bool(re.fullmatch(r'\d+', s))


# 获取所有的设备码含有分页
@pc_device.route('/Device/list/page', methods=['POST'])
def get_device_list_page():
    params = json.loads(request.data)
    sql = (
        "select tbl_d.*, tbl_c.company_name, tbl_c.link_name, tbl_u.phone, tbl_u.user_name from tbl_devices tbl_d inner join tbl_company tbl_c "
        "on tbl_c.id= tbl_d.company_id LEFT JOIN tbl_user tbl_u on tbl_u.id=tbl_d.user_id where tbl_d.is_delete=0 and tbl_c.is_delete=0 ")
    sql_count = ("select count(tbl_d.id) as total from tbl_devices tbl_d inner join tbl_company tbl_c on tbl_c.id= tbl_d.company_id LEFT JOIN "
                 "tbl_user tbl_u  on tbl_u.id=tbl_d.user_id where tbl_d.is_delete=0 and tbl_c.is_delete=0 ")

    if 'imei_code' in params and params['imei_code'] is not None and params['imei_code'] != "":
        imei_code = str(params['imei_code'])
        sql += " and tbl_d.imei_code like '%" + imei_code + "%'"
        sql_count += " and tbl_d.imei_code like '%" + imei_code + "%'"

    if 'iccid' in params and params['iccid'] is not None and params['iccid'] != "":
        iccid = str(params['iccid'])
        sql += " and tbl_d.iccid like '%" + iccid + "%'"
        sql_count += " and tbl_d.iccid like '%" + iccid + "%'"

    if 'currentCompany' in params and params['currentCompany'] is not None and params['currentCompany'] != 0 and params['currentCompany'] != 1:
        company_id = str(params['company_id'])
        sql += " and tbl_c.id =" + company_id
        sql_count += " and tbl_c.id =" + company_id

    elif 'company_id' in params and params['company_id'] is not None and params['company_id'] != 0:
        company_id = str(params['company_id'])
        sql += " and tbl_c.id =" + company_id
        sql_count += " and tbl_c.id =" + company_id
    if 'phone' in params and params['phone'] is not None and params['phone'] != "":
        phone = str(params['phone'])
        sql += " and tbl_u.phone like '%" + phone + "%'"
        sql_count += " and tbl_u.phone like '%" + phone + "%'"

    if 'is_lock' in params and params['is_lock'] is not None and params['is_lock'] != "":
        lock_status = str(params['is_lock'])
        sql += " and tbl_d.is_lock =" + lock_status
        sql_count += " and tbl_d.is_lock =" + lock_status

    if 'binding_type' in params and params['binding_type'] is not None and params['binding_type'] != "":
        if params['binding_type'] == 1:
            sql += " and tbl_d.user_id is not null"
            sql_count += " and tbl_d.user_id is not null"
        else:
            sql += " and tbl_d.user_id is null"
            sql_count += " and tbl_d.user_id is null"

    if 'create_time' in params and params['create_time'] is not None and params['create_time'] != "":
        sql += " and tbl_d.create_time >= '" + str(params['create_time'][0]) + "' and  tbl_d.create_time <= '" + str(params['create_time'][1]) + "'"
        sql_count += " and tbl_d.create_time >= '" + str(params['create_time'][0]) + "' and tbl_d.create_time <= '" + str(
            params['create_time'][1]) + "'"

    db = SQLManager()
    count_res = db.get_list(sql_count)
    total = count_res[0]['total']
    limit = params['pageSize']
    current = params['pageNum']
    min_limit = (int(current) - 1) * int(limit)
    sql += ' limit ' + str(min_limit) + ',' + str(limit)
    res_list = db.get_list(sql)
    res = {
        "list": res_list,
        "total": total,
        'pageNum': current,
        'pageSize': limit,
    }
    return JsonResponse.success(msg='success', data=res)


# 获取所有的设备码
@pc_device.route('/Device/list', methods=['POST'])
def get_device_list():
    db = SQLManager()
    sql = "select * from tbl_devices where is_delete=0"
    res_list = db.get_list(sql)
    return JsonResponse.success(msg='success', data=res_list)


# 获取设备的状态类型, 锁定/未锁定
@pc_device.route('/Device/lock/type', methods=['POST'])
def get_device_lock_type():
    res_list = [
        {
            "lockLabel": "已锁定",
            "lockValue": 1
        },
        {
            "lockLabel": "未锁定",
            "lockValue": 0
        }
    ]
    return JsonResponse.success(msg='success', data=res_list)


# 获取设备的状态类型, 锁定/未锁定
@pc_device.route('/Device/binding/type', methods=['POST'])
def get_device_binding_type():
    res_list = [
        {
            "bindingLabel": "已绑定",
            "bindingValue": 1
        },
        {
            "bindingLabel": "未绑定",
            "bindingValue": 0
        }
    ]
    return JsonResponse.success(msg='success', data=res_list)


# 添加单个设备
@pc_device.route('/Device/add', methods=['POST'])
def add_device():
    params = json.loads(request.data)
    if "imei_code" not in params or "company_id" not in params:
        return JsonResponse.fail(msg='error', data="缺少参数!")
    if is_number(params['imei_code']) is False:
        return JsonResponse.fail(msg='error', data="设备号不能包含其他字符!")
    db = SQLManager()
    try:
        imei_code = params['imei_code']
        company_id = params['company_id']
        # 判断设备是否存在
        check_sql = "select * from tbl_devices where imei_code=%s"
        check_res = db.get_one(check_sql, args=[imei_code])
        if check_res is not None:
            return JsonResponse.success(msg='error', data="设备号已存在")
        insert_imei_sql = "insert into tbl_devices (imei_code, company_id) values(%s, %s)"
        insert_device_result = db.cursor.execute(insert_imei_sql, args=[imei_code, company_id])
        device_id = db.conn.insert_id()
        if insert_device_result == 1:
            # 添加默认参数
            status_list = [
                {
                    "name": "车辆状态",
                    "code": "car_status"
                },
                {
                    "name": "警戒状态",
                    "code": "guard_status"
                },
                {
                    "name": "剩余电量",
                    "code": "endurance_status"
                },
                {
                    "name": "车锁状态",
                    "code": "lock_status"
                },
                {
                    "name": "车窗状态",
                    "code": "window_status"
                },
                {
                    "name": "静音状态",
                    "code": "mute_status"
                },
                {
                    "name": "尾门状态",
                    "code": "tailgate_status"
                },
                {
                    "name": "震动报警",
                    "code": "vibration_status"
                },
                {
                    "name": "钥匙报警",
                    "code": "key_status"
                },
                {
                    "name": "PKE状态",
                    "code": "pke_status"
                },
                {
                    "name": "刹车状态",
                    "code": "brake_status"
                },
                {
                    "name": "车辆位置",
                    "code": "location"
                },
            ]
            for item in status_list:
                if item["code"] == "location":
                    # 写入默认参数
                    insert_parameter_sql = "insert into tbl_parameter (dev_id, param_code, param_name) values(%s, %s, %s)"
                    db.cursor.execute(insert_parameter_sql,
                                      args=[device_id, item['code'], item['name']])
                else:
                    # 写入默认参数
                    insert_parameter_sql = "insert into tbl_parameter (dev_id, param_code, param_name, param_value) values(%s, %s, %s, %s)"
                    db.cursor.execute(insert_parameter_sql, args=[device_id, item['code'], item['name'], 0])

        # 添加成功后, 创建二维码图片
        db.conn.commit()
        create_qrcode_img(imei_code, company_id)
    except Exception as err:
        db.conn.rollback()
        print('添加设备码失败', err)
        return JsonResponse.fail(msg='error', data=err)
    finally:
        db.close()
    return JsonResponse.success(msg='success', data="添加成功")


# 批量添加设备
@pc_device.route('/Device/add/batch', methods=['POST'])
def add_device_batch():
    db = SQLManager()
    params = json.loads(request.data)
    if "data" not in params or "company_id" not in params:
        return JsonResponse.fail(msg='error', data="缺少参数")
    try:
        data_list = params["data"]
        company_id = params["company_id"]
        for item in data_list:
            imei_code = str(item["device_code"])
            if is_number(imei_code) is False:
                return JsonResponse.success(msg='error', data="设备号不能包含其他字符!")
                break
            check_sql = "select * from tbl_devices where imei_code=%s"
            check_res = db.get_one(check_sql, args=[imei_code])
            if check_res is not None:
                return JsonResponse.success(msg='error', data="设备号已存在")
                break
            insert_imei_sql = "insert into tbl_devices (imei_code, company_id) values(%s, %s)"
            insert_device_result = db.cursor.execute(insert_imei_sql, args=[imei_code, company_id])
            device_id = db.conn.insert_id()
            if insert_device_result == 1:
                # 添加默认参数
                status_list = [
                    {
                        "name": "车辆状态",
                        "code": "car_status"
                    },
                    {
                        "name": "警戒状态",
                        "code": "guard_status"
                    },
                    {
                        "name": "剩余电量",
                        "code": "endurance_status"
                    },
                    {
                        "name": "车锁状态",
                        "code": "lock_status"
                    },
                    {
                        "name": "车窗状态",
                        "code": "window_status"
                    },
                    {
                        "name": "静音状态",
                        "code": "mute_status"
                    },
                    {
                        "name": "尾门状态",
                        "code": "tailgate_status"
                    },
                    {
                        "name": "震动报警",
                        "code": "vibration_status"
                    },
                    {
                        "name": "钥匙报警",
                        "code": "key_status"
                    },
                    {
                        "name": "PKE状态",
                        "code": "pke_status"
                    },
                    {
                        "name": "刹车状态",
                        "code": "brake_status"
                    },
                    {
                        "name": "车辆位置",
                        "code": "location"
                    },
                ]
                for status_item in status_list:
                    if status_item["code"] == "location":
                        # 写入默认参数
                        insert_parameter_sql = "insert into tbl_parameter (dev_id, param_code, param_name) values(%s, %s, %s)"
                        db.cursor.execute(insert_parameter_sql,
                                          args=[device_id, status_item['code'], status_item['name']])
                    else:
                        # 写入默认参数
                        insert_parameter_sql = "insert into tbl_parameter (dev_id, param_code, param_name, param_value) values(%s, %s, %s, %s)"
                        db.cursor.execute(insert_parameter_sql, args=[device_id, status_item['code'], status_item['name'], 0])
            create_qrcode_img(imei_code, company_id)
        db.conn.commit()
    except Exception as err:
        db.conn.rollback()
        print('添加设备码失败', err)
        return JsonResponse.fail(msg='error', data=err)
    finally:
        db.close()
    return JsonResponse.success(msg='success', data="添加成功")


# 获取设备参数信息
@pc_device.route('/Device/parameters', methods=['POST'])
def get_device_parameters():
    params = json.loads(request.data)
    if "imei_code" not in params:
        return JsonResponse.fail(msg='error', data="数据不存在")
    imei_code = params['imei_code']
    # 判断设备是否存在
    db = SQLManager()
    check_device = (
        "select tbl_d.*, tbl_u.phone from tbl_devices tbl_d left join tbl_user tbl_u on tbl_u.id=tbl_d.user_id where tbl_d.imei_code=%s "
        "and tbl_d.is_delete=0")
    check_device_res = db.get_one(check_device, args=[imei_code])
    if check_device_res is not None:
        select_sql = (
            "select tbl_p.* from tbl_parameter tbl_p inner join tbl_devices tbl_d on tbl_d.id=tbl_p.dev_id where tbl_d.imei_code=%s and "
            "tbl_d.is_delete=0 and tbl_p.is_delete=0")
        device_res = db.get_list(select_sql, args=[imei_code])
        if device_res is not None:
            res = {
                "deviceInfo": check_device_res,
                "deviceParameters": device_res
            }
            return JsonResponse.success(msg='success', data=res)
        else:
            return JsonResponse.success(msg='error', data="数据不存在")
    else:
        return JsonResponse.success(msg='error', data="数据不存在")


# 获取车辆当前位置
@pc_device.route("/Device/location", methods=["POST"])
def get_device_location():
    params = json.loads(request.data)
    # 设备主机ID
    if "imei_code" in params and params["imei_code"] != "" and params["imei_code"] is not None:
        imei_code = params["imei_code"]
        db = SQLManager()
        select_sql = (
            "select tbl_p.* from tbl_parameter tbl_p inner join tbl_devices tbl_d on tbl_p.dev_id = tbl_d.id where tbl_d.imei_code=%s and "
            "tbl_p.is_delete=0 and tbl_d.is_delete=0 and tbl_p.param_code='location'")
        select_response = db.get_one(select_sql, args=[imei_code])
        if select_response is not None and select_response["param_value"] is not None:
            # 获取到位置后，将坐标信息转换成GCJ02坐标系
            # 将获取到的经纬度发送到高德, 获取对应的详细地址信息
            wgs84_location = select_response["param_value"].split(",")
            gcj02_location = wgs84_to_gcj02(float(wgs84_location[0]), float(wgs84_location[1]))
            location_str = str(gcj02_location[0]) + "," + str(gcj02_location[1])
            key = "1dd00d794b9a004436eb082cd259fc56"
            # # https://restapi.amap.com/v3/geocode/regeo?parameters
            url = "https://restapi.amap.com/v3/geocode/regeo?key=" + key + "&location=" + location_str
            response = requests.get(url)
            return JsonResponse.success(msg='success', data=response.json())
        else:
            return JsonResponse.success(msg='error', data="数据不存在")
    else:
        return JsonResponse.success(msg='error', data="缺少参数")


# 获取设备的绑定记录
@pc_device.route('/Device/log', methods=['POST'])
def get_device_log():
    params = json.loads(request.data)
    if "imei_code" not in params:
        return JsonResponse.fail(msg='error', data="数据不存在")
    sql = "select * from tbl_devices_user where is_delete=0 and imei_code=%s"
    sql_count = "select count(id) as total from tbl_devices_user where is_delete=0 and imei_code=%s"
    db = SQLManager()
    limit = params['pageSize']
    imei_code = params['imei_code']
    current = params['pageNum']
    count_res = db.get_list(sql_count, args=[imei_code])
    total = count_res[0]['total']
    min_limit = (int(current) - 1) * int(limit)
    sql += ' limit ' + str(min_limit) + ',' + str(limit)
    res_list = db.get_list(sql, args=[imei_code])
    res = {
        "list": res_list,
        "total": total,
        'pageNum': current,
        'pageSize': limit,
    }
    return JsonResponse.success(msg='success', data=res)


# 一键锁定设备, 禁止用户解绑设备
@pc_device.route("/Device/lock", methods=["POST"])
def control_lock():
    params = json.loads(request.data)
    if "list" in params and params["list"] != "" and params["list"] is not None:
        temp_list = params["list"]
        db = SQLManager()
        try:
            update_sql = "update tbl_devices set is_lock=1 where id in %s"
            db.cursor.execute(update_sql, args=[temp_list])
            db.conn.commit()
        except Exception as err:
            db.conn.rollback()
            print('锁定失败', err)
            return JsonResponse.fail(msg='error', data=err)
        finally:
            db.close()
        return JsonResponse.success(msg='success', data="锁定成功")
    else:
        return JsonResponse.fail(msg='error', data="缺少参数")


# 一键解锁设备, 可以允许用户解绑设备
@pc_device.route("/Device/unlock", methods=["POST"])
def control_unlock():
    params = json.loads(request.data)
    if "list" in params and params["list"] != "" and params["list"] is not None:
        temp_list = params["list"]
        db = SQLManager()
        try:
            update_sql = "update tbl_devices set is_lock=0 where id in %s"
            db.cursor.execute(update_sql, args=[temp_list])
            db.conn.commit()
        except Exception as err:
            db.conn.rollback()
            print('解锁失败', err)
            return JsonResponse.fail(msg='error', data=err)
        finally:
            db.close()
        return JsonResponse.success(msg='success', data="解锁成功")
    else:
        return JsonResponse.fail(msg='error', data="缺少参数")


# 导出设备数据
@pc_device.route('/Device/export', methods=['POST'])
def export_device():
    params = json.loads(request.data)
    sql = (
        "select tbl_d.*, tbl_c.company_name, tbl_c.link_name, tbl_u.phone, tbl_u.user_name from tbl_devices tbl_d inner join tbl_company tbl_c "
        "on tbl_c.id= tbl_d.company_id LEFT JOIN tbl_user tbl_u on tbl_u.id=tbl_d.user_id where tbl_d.is_delete=0 and tbl_c.is_delete=0 ")

    if "list" in params and params["list"] is not None and len(params["list"]) >= 1:
        temp_list = ','.join(str(x) for x in params["list"])
        sql += " and tbl_d.id in (" + temp_list + ")"
    else:
        if 'imei_code' in params and params['imei_code'] is not None and params['imei_code'] != "":
            imei_code = str(params['imei_code'])
            sql += " and tbl_d.imei_code like '%" + imei_code + "%'"

        if 'iccid' in params and params['iccid'] is not None and params['iccid'] != "":
            iccid = str(params['iccid'])
            sql += " and tbl_d.iccid like '%" + iccid + "%'"

        if 'company_id' in params and params['company_id'] is not None and params['company_id'] != 0:
            company_id = str(params['company_id'])
            sql += " and tbl_c.id =" + company_id

        if 'phone' in params and params['phone'] is not None and params['phone'] != "":
            phone = str(params['phone'])
            sql += " and tbl_u.phone like '%" + phone + "%'"

        if 'is_lock' in params and params['is_lock'] is not None and params['is_lock'] != "":
            lock_status = str(params['is_lock'])
            sql += " and tbl_d.is_lock =" + lock_status

        if 'binding_type' in params and params['binding_type'] is not None and params['binding_type'] != "":
            if params['binding_type'] == 1:
                sql += " and tbl_d.user_id is not null"
            else:
                sql += " and tbl_d.user_id is null"

        if 'create_time' in params and params['create_time'] is not None and params['create_time'] != "":
            sql += " and tbl_d.create_time >= '" + str(params['create_time'][0]) + "' and  tbl_d.create_time <= '" + str(params['create_time'][1]) + "'"
    db = SQLManager()
    res_list = db.get_list(sql)
    tempList = [
        ['设备编码', 'ICCID', '归属商家', '绑定手机号', '锁定状态', '创建时间']
    ]
    try:
        for item in res_list:
            is_lock = "未锁" if item['is_lock'] == 0 or item['is_lock'] == "0" else "已锁定"
            arr = [
                item['imei_code'],
                item['iccid'],
                item['link_name'],
                item['phone'],
                is_lock,
                datetime.datetime.strftime(item['create_time'], '%Y-%m-%d %H:%M:%S')
            ]
            tempList.append(arr)
    except Exception as err:
        print("导出设备失败", err)
        return JsonResponse.fail(msg='error', data="导出失败")

    finally:
        db.close()
    return JsonResponse.success(msg='success', data=tempList)


# 打包二维码图片文件
@pc_device.route('/Device/download/qrcode', methods=['POST'])
def download_qrcode():
    params = json.loads(request.data)
    db = SQLManager()
    # 获取需要打包的图片文件
    if "list" in params and params["list"] is not None and len(params["list"]) >= 1:
        temp_list = ','.join(str(x) for x in params["list"])
        select_sql = "select company_id, imei_code from tbl_devices where id in (" + temp_list + ")"
        res_list = db.get_list(select_sql)
    else:
        sql = (
            "select tbl_d.*, tbl_c.company_name, tbl_c.link_name, tbl_u.phone, tbl_u.user_name from tbl_devices tbl_d inner join tbl_company tbl_c "
            "on tbl_c.id= tbl_d.company_id LEFT JOIN tbl_user tbl_u on tbl_u.id=tbl_d.user_id where tbl_d.is_delete=0 and tbl_c.is_delete=0 ")

        if 'imei_code' in params and params['imei_code'] is not None and params['imei_code'] != "":
            imei_code = str(params['imei_code'])
            sql += " and tbl_d.imei_code like '%" + imei_code + "%'"

        if 'iccid' in params and params['iccid'] is not None and params['iccid'] != "":
            iccid = str(params['iccid'])
            sql += " and tbl_d.iccid like '%" + iccid + "%'"

        if 'company_id' in params and params['company_id'] is not None and params['company_id'] != 0:
            company_id = str(params['company_id'])
            sql += " and tbl_c.id =" + company_id

        if 'phone' in params and params['phone'] is not None and params['phone'] != "":
            phone = str(params['phone'])
            sql += " and tbl_u.phone like '%" + phone + "%'"

        if 'is_lock' in params and params['is_lock'] is not None and params['is_lock'] != "":
            lock_status = str(params['is_lock'])
            sql += " and tbl_d.is_lock =" + lock_status

        if 'binding_type' in params and params['binding_type'] is not None and params['binding_type'] != "":
            if params['binding_type'] == 1:
                sql += " and tbl_d.user_id is not null"
            else:
                sql += " and tbl_d.user_id is null"

        if 'create_time' in params and params['create_time'] is not None and params['create_time'] != "":
            sql += " and tbl_d.create_time >= '" + str(params['create_time'][0]) + "' and  tbl_d.create_time <= '" + str(
                params['create_time'][1]) + "'"

        res_list = db.get_list(sql)

    try:
        folder = "../background/files/qrcode/"
        # 创建一个空的内存文件，用于存储压缩文件
        in_memory_zip = BytesIO()
        with zipfile.ZipFile(in_memory_zip, 'w', compression=zipfile.ZIP_DEFLATED) as zf:
            # 遍历目录下的所有文件，并将文件添加到zip文件中
            for item in res_list:
                file_path = folder + str(item["company_id"]) + "/" + str(item["imei_code"]) + ".png"
                file_name = str(item["imei_code"]) + ".png"
                # 将文件添加到zip文件中，并指定压缩文件中的文件名为文件相对路径
                zf.write(file_path, arcname=file_name)
        # 将内存文件指针移动到文件开头
        in_memory_zip.seek(0)
        # 返回压缩文件，指定文件名为download.zip
        blob_data = send_file(in_memory_zip, download_name='download.zip', as_attachment=True)
        return blob_data
    except Exception as err:
        print("二维码图片打包失败", err)
        return JsonResponse.fail(msg='error', data="二维码图片打包失败")
