from flask import request, Blueprint
from api.config.json_response import JsonResponse
from api.mqtt.mqtt import *

mini_control = Blueprint('mini_control', __name__)

"""
GPS警戒指令： 		B5010000B6;
GPS解锁指令： 		B5020000B7;
GPS静音警戒指令：	 	B5030000B8;
GPS启动指令： 	 	B5040000B9;
GPS开尾门指令： 	 	B5050000BA;
查询全部状态指令： 	B50F0000C5;
"""
# 请求接口==>mqtt获取数据并保存==>将数据返回给前端请求
# MQTT 配置
my_mqtt_client = MQTTClient(broker_address, mqtt_port, 0, mqtt_client_id, 60, username, password)
# 连接mqtt并且订阅数据
my_mqtt_client.connect()
# mqtt_client.usr_subscribe("GZMR/#")


# 控制警戒状态====关锁?
@mini_control.route("/mini/control/guard", methods=["POST"])
def control_guard():
    params = json.loads(request.data)
    instruction_str = "B5010000B6"
    if "imei_code" in params and params["imei_code"] != "" and params["imei_code"] is not None:
        imei_code = params["imei_code"]
        topic = 'GZMR/CONTROL/' + str(imei_code)
        payload = bytes.fromhex(instruction_str)
        publish_res = my_mqtt_client.usr_publish(topic, payload, 0)
        if publish_res.rc == 0:
            return JsonResponse.success(msg='success', data="指令发送成功")
        else:
            return JsonResponse.success(msg='error', data="指令发送失败")
    else:
        return JsonResponse.fail(msg='error', data="缺少参数")


# 控制车锁
@mini_control.route("/mini/control/open_lock", methods=["POST"])
def control_open_lock():
    params = json.loads(request.data)
    instruction_str = "B5020000B7"
    if "imei_code" in params and params["imei_code"] != "" and params["imei_code"] is not None:
        imei_code = params["imei_code"]
        topic = 'GZMR/CONTROL/' + str(imei_code)
        payload = bytes.fromhex(instruction_str)
        publish_res = my_mqtt_client.usr_publish(topic, payload, 0)
        if publish_res.rc == 0:
            return JsonResponse.success(msg='success', data="指令发送成功")
        else:
            return JsonResponse.success(msg='error', data="指令发送失败")
    else:
        return JsonResponse.fail(msg='error', data="缺少参数")


# 控制关锁
@mini_control.route("/mini/control/close_lock", methods=["POST"])
def control_close_lock():
    params = json.loads(request.data)
    instruction_str = "B5020000B7"
    if "imei_code" in params and params["imei_code"] != "" and params["imei_code"] is not None:
        imei_code = params["imei_code"]
        topic = 'GZMR/CONTROL/' + str(imei_code)
        payload = bytes.fromhex(instruction_str)
        publish_res = my_mqtt_client.usr_publish(topic, payload, 0)
        if publish_res.rc == 0:
            return JsonResponse.success(msg='success', data="指令发送成功")
        else:
            return JsonResponse.success(msg='error', data="指令发送失败")
    else:
        return JsonResponse.fail(msg='error', data="缺少参数")


# 控制车辆静音
@mini_control.route("/mini/control/mute", methods=["POST"])
def control_mute():
    params = json.loads(request.data)
    instruction_str = "B5030000B8"
    if "imei_code" in params and params["imei_code"] != "" and params["imei_code"] is not None:
        imei_code = params["imei_code"]
        topic = 'GZMR/CONTROL/' + str(imei_code)
        payload = bytes.fromhex(instruction_str)
        publish_res = my_mqtt_client.usr_publish(topic, payload, 0)
        if publish_res.rc == 0:
            return JsonResponse.success(msg='success', data="指令发送成功")
        else:
            return JsonResponse.success(msg='error', data="指令发送失败")
    else:
        return JsonResponse.fail(msg='error', data="缺少参数")


# 控制车辆启动
@mini_control.route("/mini/control/start", methods=["POST"])
def control_start():
    params = json.loads(request.data)
    instruction_str = "B5010000B9"
    if "imei_code" in params and params["imei_code"] != "" and params["imei_code"] is not None:
        imei_code = params["imei_code"]
        topic = 'GZMR/CONTROL/' + str(imei_code)
        payload = bytes.fromhex(instruction_str)
        publish_res = my_mqtt_client.usr_publish(topic, payload, 0)
        if publish_res.rc == 0:
            return JsonResponse.success(msg='success', data="指令发送成功")
        else:
            return JsonResponse.success(msg='error', data="指令发送失败")
    else:
        return JsonResponse.fail(msg='error', data="缺少参数")


# 控制后备箱
@mini_control.route("/mini/control/trunk", methods=["POST"])
def control_trunk():
    params = json.loads(request.data)
    instruction_str = "B5050000BA"
    if "imei_code" in params and params["imei_code"] != "" and params["imei_code"] is not None:
        imei_code = params["imei_code"]
        topic = 'GZMR/CONTROL/' + str(imei_code)
        payload = bytes.fromhex(instruction_str)
        publish_res = my_mqtt_client.usr_publish(topic, payload, 0)
        if publish_res.rc == 0:
            return JsonResponse.success(msg='success', data="指令发送成功")
        else:
            return JsonResponse.success(msg='error', data="指令发送失败")
    else:
        return JsonResponse.fail(msg='error', data="缺少参数")
