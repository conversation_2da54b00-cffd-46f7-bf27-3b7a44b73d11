from aliyunsdkcore.client import AcsClient
from aliyunsdkcore.request import CommonRequest
# 阿里云短信API的配置信息
access_key_id = "LTAIqNM8S02raiaD"
access_key_secret = "icHBYGTs3j4GUxfDc1bQgn59Y4ERmL"
sign_name = "欧信科技"
template_code = "SMS_468265513"


# 发送短信通知
def send_notice(phone, time):
    client = AcsClient(access_key_id, access_key_secret, "default")

    request = CommonRequest()
    request.set_method("POST")
    request.set_domain("dysmsapi.aliyuncs.com")
    request.set_version("2017-05-25")
    request.set_action_name("SendSms")

    request.add_query_param("PhoneNumbers", phone)
    request.add_query_param("SignName", sign_name)
    request.add_query_param("TemplateCode", template_code)
    request.add_query_param("TemplateParam", f"{{\"remark\":\"{time}\"}}")

    response = client.do_action_with_exception(request)
    print("短信发送结果：", response.decode())