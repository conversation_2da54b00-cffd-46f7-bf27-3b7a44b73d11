import json
from flask import request, Blueprint
from api.config.json_response import JsonResponse
from api.config.config import *
import hashlib
from datetime import datetime
from api.config.util_jwt import Util_jwt

pc_company = Blueprint('pc_company', __name__)
util_jwt = Util_jwt()


# 登录平台
@pc_company.route("/login", methods=["POST"])
def login():
    data = json.loads(request.data)
    m = hashlib.md5()
    m.update(data['password'].encode('utf-8'))
    password = m.hexdigest()
    company_name = data['username']
    sql = ("select tbl_c.*,r.description,r.role_name from tbl_company tbl_c inner join sys_role r on r.id=tbl_c.role_id where tbl_c.company_name=%s "
           "and tbl_c.password=%s limit 1")
    db = SQLManager()
    result = db.get_one(sql, args=[company_name, password])
    db.close()
    if result is not None:
        token = util_jwt.encryption_token(result['id'], result['company_name'])
        res = {
            "userInfo": result,
            "token": token
        }
        return JsonResponse.success(msg='success', data=res)
    else:
        return JsonResponse.fail(msg='error', data='账号或密码错误')


# 退出登录
@pc_company.route("/logout", methods=["POST"])
def logout():
    # 进行其他操作
    print("退出登录数据")


# 获取登录公司数据
@pc_company.route("/company/info", methods=["post"])
def get_company_info():
    params = json.loads(request.data)
    if "company_id" not in params:
        return JsonResponse.fail(msg='error', data="缺少参数")
    sql = "select * from tbl_company where is_delete=0 and id=%s"
    db = SQLManager()
    res = db.get_one(sql, args=[params["company_id"]])
    db.close()
    return JsonResponse.success(msg='success', data=res)


# 修改密码
@pc_company.route("/company/password/update", methods=["post"])
def update_company_password():
    data = json.loads(request.data)
    db = SQLManager()
    try:
        old = hashlib.md5()
        new = hashlib.md5()
        old.update(data['oldPassword'].encode('utf-8'))
        new.update(data['newPassword'].encode('utf-8'))
        old_password = old.hexdigest()
        new_password = new.hexdigest()
        # 检查旧密码是否正确
        check_company = "select id from tbl_company where id=%s and password=%s"
        check_res = db.get_one(check_company, args=[data['id'], old_password])
        if check_res is not None:
            update_sql = "update tbl_company set password=%s where id=%s"
            db.modify(update_sql, args=[new_password, data['id']])
        else:
            return JsonResponse.fail(msg="error", data="旧密码不正确")
    except Exception as err:
        print("密码修改错误", err)
        db.conn.rollback()
        return JsonResponse.fail(msg='error', data=err)
    finally:
        db.close()
    return JsonResponse.success(msg="success", data="修改成功")


# 添加公司
@pc_company.route("/company/add", methods=["POST"])
def add_company():
    data = json.loads(request.data)
    db = SQLManager()
    check_repeat = db.get_list('select id from tbl_company where company_name=%s and is_delete=0', args=[data['company_name']])
    if len(check_repeat) >= 1:
        return JsonResponse.fail(msg='error', data='公司名称已存在')
    else:
        try:
            m = hashlib.md5()
            m.update(data['password'].encode('utf-8'))
            password = m.hexdigest()
            # 添加数据到公司表
            insert_company = 'insert into tbl_company (company_name, password, phone, link_name, role_id) values(%s,%s,%s,%s,%s)'
            db.cursor.execute(insert_company, args=[data['company_name'], password, data['phone'], data['link_name'], data['role_id']])
            user_id = db.conn.insert_id()
            # 添加数据到角色和公司关联表
            insert_role = 'insert into sys_company_role (user_id, role_id) values(%s,%s)'
            db.cursor.execute(insert_role, args=[user_id, data['role_id']])
            db.conn.commit()
        except Exception as err:
            print('添加公司失败', err)
            db.conn.rollback()
            return JsonResponse.fail(msg='error', data=err)
        finally:
            db.close()
        return JsonResponse.success(msg='success', data='添加成功')


# 更新公司
@pc_company.route("/company/update", methods=["POST"])
def update_company():
    data = json.loads(request.data)
    db = SQLManager()
    check_repeat = db.get_list('select id from tbl_company where company_name=%s and is_delete=0 and id!=%s', args=[data['company_name'], data['id']])
    if len(check_repeat) >= 1:
        return JsonResponse.fail(msg='error', data='当前公司名称已存在')
    else:
        try:
            update_company_sql = 'update tbl_company set company_name=%s, phone=%s, link_name=%s, role_id=%s where id=%s'
            db.cursor.execute(update_company_sql, args=[data['company_name'], data['phone'], data['link_name'], data['role_id'], data['id']])
            # 判断关联的公司角色是否一致 , 不一致的话先将关联的数据删除后再重新添加关联
            check_company_role = db.get_one('select * from sys_company_role where user_id=%s and is_delete=0', args=[data['id']])
            if check_company_role['role_id'] != data['role_id']:
                update_company_role = 'update sys_company_role set is_delete=1 where id=%s'
                db.cursor.execute(update_company_role, args=[check_company_role['id']])
                insert_company_role = 'insert into sys_company_role (user_id, role_id) values(%s, %s)'
                db.cursor.execute(insert_company_role, args=[data['id'], data['role_id']])
            db.conn.commit()
        except Exception as err:
            print('更新公司失败', err)
            db.conn.rollback()
            return JsonResponse.fail(msg='error', data=err)
        finally:
            db.close()
        return JsonResponse.success(msg='success', data='更新成功')


# 删除公司
@pc_company.route("/company/delete", methods=["POST"])
def delete_company():
    data = json.loads(request.data)
    db = SQLManager()
    try:
        # 删除公司表
        delete_company_sql = 'update tbl_company set is_delete=1 where id=%s'
        db.cursor.execute(delete_company_sql, args=[data['id']])
        # 删除公司角色关联表
        delete_company_role_sql = 'update sys_company_role set is_delete=1 where user_id=%s'
        db.cursor.execute(delete_company_role_sql, args=[data['id']])
        db.conn.commit()
    except Exception as err:
        print('删除公司失败', err)
        db.conn.rollback()
        return JsonResponse.fail(msg='error', data=err)
    finally:
        db.close()
    return JsonResponse.success(msg='success', data='删除成功')


# 公司列表--分页
@pc_company.route("/company/list/page", methods=["POST"])
def get_company_list_page():
    data = json.loads(request.data)
    db = SQLManager()
    sql = "select tbl_c.*,r.description,r.role_name from tbl_company tbl_c inner join sys_role r on r.id=tbl_c.role_id where tbl_c.is_delete=0 "
    sql_count = "select count(tbl_c.id) as total from tbl_company tbl_c inner join sys_role r on r.id=tbl_c.role_id where tbl_c.is_delete=0 "
    count_res = db.get_list(sql_count)
    total = count_res[0]['total']
    limit = data['pageSize']
    current = data['pageNum']
    limit_min = (int(current) - 1) * int(limit)
    sql += ' limit ' + str(limit_min) + ',' + str(limit)
    res_list = db.get_list(sql)
    res = {
        "list": res_list,
        "pageNum": current,
        "pageSize": limit,
        "total": total
    }
    return JsonResponse.success(msg='success', data=res)


# 公司列表--分页
@pc_company.route("/company/list", methods=["POST"])
def get_company_list():
    params = json.loads(request.data)
    if "company_id" in params:
        company_id = params["company_id"]
        db = SQLManager()
        # 先判断当前用户是不是总管理员
        check_login_company = "select * from tbl_company where id=%s"
        check_res = db.get_one(check_login_company, args=[company_id])
        if check_res["role_id"] == 1 and company_id == 1:
            # 当前用户是总管理员, 返回所有的商家信息
            sql = ("select tbl_c.company_name,tbl_c.phone,tbl_c.link_name,tbl_c.id,tbl_c.link_name,r.description,r.role_name from tbl_company tbl_c "
                   "inner join  sys_role r on r.id=tbl_c.role_id where tbl_c.is_delete=0 ")
            count_res = db.get_list(sql)
            return JsonResponse.success(msg='success', data=count_res)
        else:
            sql = ("select tbl_c.*,r.description,r.role_name from tbl_company tbl_c inner join sys_role r on r.id=tbl_c.role_id where "
                   "tbl_c.is_delete=0 and  tbl_c.id=%s")
            count_res = db.get_list(sql, args=[company_id])
            return JsonResponse.success(msg='success', data=count_res)

    else:
        return JsonResponse.fail(msg="error", data="缺少参数")


# 导出公司列表
@pc_company.route("/company/export", methods=["POST"])
def export_company():
    db = SQLManager()
    data = json.loads(request.data)
    limit = data['pageSize']
    current = data['pageNum']
    limit_min = (int(current) - 1) * int(limit)
    select_sql = "select tbl_c.*,r.description,r.role_name from tbl_company tbl_c inner join sys_role r on r.id=tbl_c.role_id where tbl_c.is_delete=0"
    select_sql += ' limit ' + str(limit_min) + ',' + str(limit)
    res_list = db.get_list(select_sql)
    temp_list = [
        ['公司名称', '联系人', '联系电话', '角色身份', '创建时间']
    ]
    for item in res_list:
        arr = [
            item['company_name'],
            item['link_name'],
            item['phone'],
            item['role_name'],
            datetime.strftime(item['create_time'], '%Y-%m-%d %H:%M:%S')
        ]
        temp_list.append(arr)
    db.close()
    return JsonResponse.success(msg="success", data=temp_list)
