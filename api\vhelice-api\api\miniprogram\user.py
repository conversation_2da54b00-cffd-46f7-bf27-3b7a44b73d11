import json
import hashlib
import requests
from flask import request, Blueprint
from api.config.json_response import JsonResponse
from api.config.config import *
from api.config.public import generate_random_number
from api.config.send_code import send_verification_code

mini_user = Blueprint('mini_user', __name__)
# 小程序的 rapid 和 app secret
app_id = "wxcecdaaa94f649478"
app_secret = "30eee4435d8cd4e2605bb3a7e905695d"

# 用户登录时 , 判断设备套餐是否到期


# 登录平台
@mini_user.route("/mini/user/login", methods=["POST"])
def wx_login():
    # 请求微信接口, 获取微信用户信息, 绑定手机号, 存入数据库
    params = json.loads(request.data)
    if params.get('code'):

        js_code = params["code"]
        # 登录凭证校验, 获取sessionKey和openid
        url = f'https://api.weixin.qq.com/sns/jscode2session?appid={app_id}&secret={app_secret}&js_code={js_code}&grant_type=authorization_code'
        wx_response = requests.post(url).json()
        if "errcode" not in wx_response:
            session_key = wx_response["session_key"]
            openid = wx_response["openid"]
            signature = hashlib.md5((session_key + '&' + openid).encode()).hexdigest()
            db = SQLManager()
            # 将数据保存到数据库
            try:
                # 将用户数据存入数据库
                check_user_sql = "select id from tbl_user where openid=%s and is_delete=0 limit 1"
                check_result = db.get_one(check_user_sql, args=[openid])
                if check_result is None:
                    # 没有数据直接添加
                    insert_user = "insert into tbl_user(openid, session_key, session_3rd) values(%s, %s, %s)"
                    db.cursor.execute(insert_user, args=[openid, session_key, signature])
                    db.conn.commit()
                else:
                    # 已有数据修改随机字符串的值
                    insert_user = "update tbl_user set session_3rd=%s where openid=%s"
                    db.cursor.execute(insert_user, args=[signature, openid])
                    db.conn.commit()
            except Exception as e:
                print(e)
                db.conn.rollback()
                return JsonResponse.fail(msg="error", data=e)
            finally:
                db.close()
            return JsonResponse.success(msg="success", data={"signature": signature})
        else:
            return JsonResponse.fail(msg="error", data="获取session_key失败,请重新进入小程序")
    else:
        return JsonResponse.fail(msg="error", data="缺少参数,请重新进入小程序")


# 获取登录用户数据
@mini_user.route("/mini/user/info", methods=["post"])
def get_user_info():
    params = json.loads(request.data)
    # 判断小程序是否传入这些参数
    if params.get('session_3rd'):
        session_3rd = params['session_3rd']
        db = SQLManager()
        check_user_sql = "select id,avatar,user_name,phone,imei_code,create_time,openid from tbl_user where session_3rd=%s and is_delete=0 limit 1"
        check_result = db.get_one(check_user_sql, args=[session_3rd])
        if check_result is not None:
            return JsonResponse.success(msg='success', data=check_result)
        else:
            return JsonResponse.fail(msg='error', data="session_3rd已过期")
    else:
        return JsonResponse.fail(msg='error', data="缺少参数")


# 获取登录用户手机号
@mini_user.route("/mini/wechat/phone", methods=["post"])
def get_wechat_phone():
    params = json.loads(request.data)
    if params.get('code'):
        phone_code = params["code"]
        session_3rd = params["session_3rd"]
        # 获取token
        token_url = f'https://api.weixin.qq.com/cgi-bin/token?grant_type=client_credential&appid={app_id}&secret={app_secret}'
        response_token = requests.post(token_url)
        access_token = response_token.json()['access_token']
        headers = {"Content-Type": "application/json"}
        para = {'code': phone_code}
        # 获取手机号
        phone_url = f"https://api.weixin.qq.com/wxa/business/getuserphonenumber?access_token={access_token}"
        response_phone = requests.post(phone_url, json=para, headers=headers)
        phone_result = response_phone.json()
        if phone_result['errcode'] == 0:
            # 将数据保存到数据库
            db = SQLManager()
            phone_info = phone_result['phone_info']
            try:
                insert_user = "update tbl_user set phone=%s where session_3rd=%s"
                db.cursor.execute(insert_user, args=[phone_info["phoneNumber"], session_3rd])
                db.conn.commit()
            except Exception as e:
                print(e)
                db.conn.rollback()
                return JsonResponse.fail(msg="error", data=e)
            finally:
                db.close()
        return JsonResponse.success(msg="success", data="绑定成功")
    else:
        return JsonResponse.fail(msg="error", data="缺少参数,请重新进入小程序")


# 发送验证码(绑定/解绑设备)
@mini_user.route("/mini/user/send_code", methods=["post"])
def send_code():
    # 通过手机号发送验证码
    params = json.loads(request.data)
    if "phone" not in params:
        return JsonResponse.fail(msg="error", data="缺少参数")
    else:
        phone_number = params["phone"]  # 接收验证码的手机号
        # 验证码为ICCID的后六位
        verification_code = generate_random_number()  # 生成验证码
        db = SQLManager()
        try:
            # 判断当前手机号是否有记录, 有的话直接更新
            check_sql = "select * from tbl_phone_code where phone=%s"
            check_res = db.get_one(check_sql, args=[phone_number])
            if check_res is not None:
                update_sql = "update tbl_phone_code set code=%s, is_delete=0 where phone=%s"
                db.cursor.execute(update_sql, args=[verification_code, phone_number])
            else:
                insert_sql = "insert into tbl_phone_code (phone, code) values(%s, %s)"
                db.cursor.execute(insert_sql, args=[phone_number, verification_code])
            db.conn.commit()
            send_verification_code(phone_number, verification_code)
        except Exception as e:
            db.conn.rollback()
            print(e)
            return JsonResponse.fail(msg="error", data="验证码发送失败")
        finally:
            db.close()
        return JsonResponse.success(msg="success", data="验证码发送成功")


# 绑定设备
@mini_user.route("/mini/user/binding", methods=["post"])
def binding_device():
    params = json.loads(request.data)
    if "dev_code" not in params or "phone" not in params or "code" not in params or "user_id" not in params:
        return JsonResponse.fail(msg="error", data="缺少参数")
    else:
        user_id = params["user_id"]
        phone_number = params["phone"]
        verification_code = params["code"]
        imei_code = params["dev_code"]
        db = SQLManager()
        try:
            # 判断验证码是否正确
            check_code_sql = ("select id from tbl_phone_code where phone=%s and code=%s and is_delete=0 and TIMESTAMPDIFF(MINUTE,update_time, "
                              "now()) <= 5")
            check_code = db.get_one(check_code_sql, args=[phone_number, verification_code])
            if check_code is not None:
                # 判断设备是否存在
                check_imei_sql = "select id from tbl_devices where imei_code=%s and is_delete=0"
                check_imei_res = db.get_one(check_imei_sql, args=[imei_code])
                if check_imei_res is not None:
                    # 判断设备是否已经被绑定
                    check_binding_sql = "select id from tbl_devices where imei_code=%s and is_delete=0 and (user_id is not null or user_id != '') "
                    check_binding_res = db.get_one(check_binding_sql, args=[imei_code])
                    if check_binding_res is None:
                        # 更新相关数据信息
                        # 验证码设置为无效
                        update_code_status = "update tbl_phone_code set is_delete=1 where phone=%s and is_delete=0"
                        db.cursor.execute(update_code_status, args=[phone_number])
                        # 更新用户数据
                        update_user = "update tbl_user set imei_code=%s where phone=%s and is_delete=0"
                        db.cursor.execute(update_user, args=[imei_code, phone_number])
                        # 判断设备是不是第一次绑定? 是: 添加绑定时间, 不是的话就不用操作
                        check_frequency_sql = "select id from tbl_devices_user where imei_code=%s and is_delete=0"
                        check_frequency_res = db.get_one(check_frequency_sql, args=[imei_code])
                        # 更新imei列表数据
                        is_first = 0
                        update_imei = "update tbl_devices set user_id=%s where imei_code=%s and is_delete=0"
                        insert_imei_user = "insert into tbl_devices_user (user_phone, imei_code, binding_type, is_first) values(%s, %s, %s, %s)"
                        if check_frequency_res is None:
                            # 第一次绑定
                            update_imei = "update tbl_devices set user_id=%s, binding_time=NOW() where imei_code=%s and is_delete=0"
                            is_first = 1
                        db.cursor.execute(update_imei, args=[user_id, imei_code])
                        db.cursor.execute(insert_imei_user, args=[phone_number, imei_code, 1, is_first])
                        db.conn.commit()
                    else:
                        return JsonResponse.success(msg="error", data="设备号已被绑定")
                else:
                    return JsonResponse.success(msg="error", data="设备号不存在")
            else:
                return JsonResponse.success(msg="error", data="验证码无效或已过期")
        except Exception as e:
            db.conn.rollback()
            print(e)
            return JsonResponse.fail(msg="error", data="绑定失败")
        finally:
            db.close()
        return JsonResponse.success(msg="success", data="绑定成功")


# 解绑设备
@mini_user.route("/mini/user/unbinding", methods=["post"])
def unbinding_device():
    params = json.loads(request.data)
    if "dev_code" not in params or "phone" not in params or "code" not in params:
        return JsonResponse.fail(msg="error", data="缺少参数")
    else:
        phone_number = params["phone"]
        verification_code = params["code"]
        imei_code = params["dev_code"]
        db = SQLManager()
        try:
            # 判断验证码是否正确
            check_code_sql = ("select id from tbl_phone_code where phone=%s and code=%s and is_delete=0 and TIMESTAMPDIFF(MINUTE,update_time, "
                              "now()) <= 5")
            check_code = db.get_one(check_code_sql, args=[phone_number, verification_code])
            if check_code is not None:
                # 判断设备是否存在
                check_imei_sql = "select * from tbl_devices where imei_code=%s and is_delete=0"
                check_imei_res = db.get_one(check_imei_sql, args=[imei_code])
                if check_imei_res is not None:
                    # 更新相关数据
                    # 判断设备是否为锁定状态, 锁定状态时, 禁止用户解锁设备
                    if check_imei_res["is_lock"] != "1" and check_imei_res["is_lock"] != 1:
                        # 验证码设置为无效
                        update_code_status = "update tbl_phone_code set is_delete=1 where phone=%s and is_delete=0"
                        db.cursor.execute(update_code_status, args=[phone_number])

                        # 更新用户数据
                        update_user = "update tbl_user set imei_code=null where phone=%s and is_delete=0"
                        db.cursor.execute(update_user, args=[phone_number])

                        # 更新imei列表数据
                        update_imei = "update tbl_devices set user_id=null where imei_code=%s and is_delete=0"
                        insert_imei_user = "insert into tbl_devices_user (user_phone, imei_code, binding_type, is_first) values(%s, %s, %s, %s)"
                        db.cursor.execute(update_imei, args=[imei_code])
                        db.cursor.execute(insert_imei_user, args=[phone_number, imei_code, 0, 0])
                        db.conn.commit()
                    else:
                        return JsonResponse.success(msg="error", data="设备已锁定,禁止解绑")
                else:
                    return JsonResponse.success(msg="error", data="设备号不存在")
            else:
                return JsonResponse.success(msg="error", data="验证码无效或已过期")
        except Exception as e:
            db.conn.rollback()
            print(e)
            return JsonResponse.fail(msg="error", data="解绑失败")
        finally:
            db.close()
        return JsonResponse.success(msg="success", data="解绑成功")
