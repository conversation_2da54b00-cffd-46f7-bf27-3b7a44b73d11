import hashlib
import json
import requests
import datetime as datetime

from flask import request, Blueprint

from api.config.config import *
from api.config.json_response import JsonResponse
from api.config.public import generate_random_number

pc_cards = Blueprint('pc_cards', __name__)


# 卡片管理
# 查询出已经到期的设备
# 批量或者单个 控制设备 停用/激活
# 1: 固定流量超额后, 提醒用户充值, 停用设备, 充值后恢复使用
# 2: 有效时间到期, 提醒用户充值, 停用设备, 充值后恢复使用
# 查询一个或多个设备详情 https://gwapi.10646.cn/api/wsGetTerminalDetails/V1/1Main/vV1.1
# 为给定设备更改单个属性 https://gwapi.10646.cn/api/wsEditTerminal/V1/1Main/vV1.1
# 查询设备用量 https://gwapi.10646.cn/api/wsGetTerminalUsage/V1/1Main/vV1.1
# 查询设备用量详情 https://gwapi.10646.cn/api/wsGetTerminalUsageDataDetails/V1/1Main/vV1.1

# 根据不同接口生成请求参数
def generate_params(app_id, app_secret):
    current_time = datetime.datetime.now()
    time_format = datetime.datetime.strftime(current_time, '%Y-%m-%d %H:%M:%S')
    time_str = datetime.datetime.strftime(current_time, '%Y%m%d%H%M%S')
    # 毫秒的前三位数字
    milliseconds = str(current_time.microsecond)[0:3]
    random_number = generate_random_number()
    # timestamp
    timestamp = time_format + " " + milliseconds
    # trans_id
    trans_id = str(time_str) + milliseconds + str(random_number)
    sign_str = "app_id" + app_id + "timestamp" + timestamp + "trans_id" + trans_id + app_secret
    # token
    sm3 = hashlib.new('sm3')
    sm3.update(sign_str.encode('utf-8'))
    token = sm3.hexdigest()
    open_id = "38951oueWSgV3pZ"
    result = {
        "timestamp": timestamp,
        "trans_id": trans_id,
        "token": token,
        "open_id": open_id
    }
    return result


# 自动轮询设备
def auto_polling():
    print("自动轮询设备")
    """
    1. 查询数据库中绑定卡片
    2. 判断卡片的有效时间是否到期
    3. 判断卡片是否超用流量
    4. 控制卡片停用和激活
    """
    # 查询已绑定的设备, 判断设备是否已经到期
    db = SQLManager()

    select_devices = "select iccid, effective_time from tbl_devices where is_delete=0"
    cards_list = db.get_list(select_devices)
    for item in cards_list:
        # 判断有效期是否已经失效
        if item["effective_time"] is not None and item["effective_time"] != "":
            current_time = datetime.datetime.now()
            effective_time = item["effective_time"]
            if current_time > effective_time:
                print("有效期过了, 自动修改卡片状态")

        # 查询设备是否超流量
        # get_usage()
        url = "https://gwapi.10646.cn/api/wsGetTerminalUsage/V1/1Main/vV1.1"
        app_id = "GAg7wBXpOW"
        app_secret = "50kl63JTXKnLD3ez8lMv1kuCg9CJVE"
        result = generate_params(app_id, app_secret)
        cards_params = {
            "app_id": app_id,
            "timestamp": result["timestamp"],
            "trans_id": result["trans_id"],
            "token": result["token"],
            "data": {
                "openId": result["open_id"],
                "version": "1.0",
                "iccid": "89860623500003992390",
                "billingCycle": "202405"
            }
        }

# auto_polling()


# 查询一个或多个设备详情
@pc_cards.route("/cards/details", methods=["POST"])
def get_vehicle_location():
    print("查询设备详情")
    params = json.loads(request.data)
    print(params)
    app_id = "zHNd4GiVoV"
    app_secret = "u4Q3Jk43rUt33BVurA9n19jKuF2CBB"
    result = generate_params(app_id, app_secret)
    cards_params = {
        "app_id": app_id,
        "timestamp": result["timestamp"],
        "trans_id": result["trans_id"],
        "token": result["token"],
        "data": {
            "openId": result["open_id"],
            "version": "1.0",
            "iccids": ["89860623500003992382", "89860623500003992390"]
        }
    }
    url = "https://gwapi.10646.cn/api/wsGetTerminalDetails/V1/1Main/vV1.1"
    response = requests.post(url, data=json.dumps(cards_params))
    print(response.json())
    return JsonResponse.success(msg='success', data=response.json())


# 启用或停用设备
@pc_cards.route("/cards/edit", methods=["POST"])
def change_type():
    print("更改设备属性值")
    params = json.loads(request.data)
    print(params)
    # 更改设备属性
    # 89860623500003992382, 89860623500003992390
    # 获取设备当前流量使用情况, 判断是否有超出套餐, 计算超出部分的费用, 判断账户余额? 最后停止使用
    # targetValue; 2: 已激活, 3: 已停用
    target_value = "2"
    app_id = "b8t9cnyWwP"
    app_secret = "2Ipos25VWEUSLeXNgIPMvKjP1qW098"
    result = generate_params(app_id, app_secret)
    cards_params = {
        "app_id": app_id,
        "timestamp": result["timestamp"],
        "trans_id": result["trans_id"],
        "token": result["token"],
        "data": {
            "messageId": result["trans_id"],
            "openId": result["open_id"],
            "version": "1.0",
            "asynchronous": "0",
            "changeType": "3",
            "targetValue": target_value,
            "iccid": "89860621280094270228",
        }
    }
    url = "https://gwapi.10646.cn/api/wsEditTerminal/V1/1Main/vV1.1"
    response = requests.post(url, data=json.dumps(cards_params))
    print(response.json())
    return JsonResponse.success(msg='success', data=response.json())


# 查询设备用量
@pc_cards.route("/cards/usage", methods=["POST"])
def get_usage():
    print("查询设备用量情况")
    params = json.loads(request.data)
    print(params)
    url = "https://gwapi.10646.cn/api/wsGetTerminalUsage/V1/1Main/vV1.1"
    app_id = "GAg7wBXpOW"
    app_secret = "50kl63JTXKnLD3ez8lMv1kuCg9CJVE"
    result = generate_params(app_id, app_secret)
    cards_params = {
        "app_id": app_id,
        "timestamp": result["timestamp"],
        "trans_id": result["trans_id"],
        "token": result["token"],
        "data": {
            "openId": result["open_id"],
            "version": "1.0",
            "iccid": "89860623500003992390",
            "billingCycle": "202405"
        }
    }
    # billableDataVolume: 计费流量 , 查看对应的资费计划, 然后计算出超出部分流量所需的金额
    response = requests.post(url, data=json.dumps(cards_params))
    print(response.json())
    return JsonResponse.success(msg='success', data=response.json())
