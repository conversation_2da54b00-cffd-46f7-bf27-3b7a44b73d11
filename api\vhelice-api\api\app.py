from flask_cors import *
from api.config.json_flask import JsonFlask
# PC端
from api.pc.device import pc_device
from api.pc.system import pc_system
from api.pc.user import pc_user
from api.pc.company import pc_company
from api.pc.role import pc_role
from api.pc.upload import pc_upload
from api.pc.packets import pc_packets
from api.pc.orders import pc_orders
from api.pc.cards import pc_cards
# 小程序
from api.miniprogram.user import mini_user
from api.miniprogram.control import mini_control
from api.miniprogram.vehicle import mini_vehicle
from api.miniprogram.packets import mini_packets
from api.miniprogram.orders import mini_orders


# 创建视图应用
app = JsonFlask(__name__)
# 配置上传文件大小

# PC管理后台相关
app.register_blueprint(pc_device)
app.register_blueprint(pc_system)
app.register_blueprint(pc_user)
app.register_blueprint(pc_company)
app.register_blueprint(pc_role)
app.register_blueprint(pc_upload)
app.register_blueprint(pc_packets)
app.register_blueprint(pc_orders)
app.register_blueprint(pc_cards)
# 小程序相关
app.register_blueprint(mini_user)
app.register_blueprint(mini_control)
app.register_blueprint(mini_vehicle)
app.register_blueprint(mini_packets)
app.register_blueprint(mini_orders)

# 解决跨域
CORS(app, supports_credentials=True)
