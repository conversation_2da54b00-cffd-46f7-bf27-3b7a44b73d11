import json
import logging
import os
import time
import uuid
from api.config.config import *
from flask import request, Blueprint
from api.config.json_response import JsonResponse
import datetime as datetime
from dateutil.relativedelta import relativedelta
from wechatpayv3 import WeChatPay, WeChatPayType

mini_orders = Blueprint('mini_orders', __name__)

# 证书序列号: 4E9BA2A23189ADAF9DBCA6766A96C8BD93E183B2
# 配置参数
# 商户证书私钥，此文件不要放置在下面设置的CERT_DIR目录里。
with open('./cert/cert_key/ox20240626_key.pem') as f:
    PRIVATE_KEY = f.read()

# 商户号
MCH_ID = "1425621202"
# API证书序列号
CERT_SERIAL_NO = "4E9BA2A23189ADAF9DBCA6766A96C8BD93E183B2"
# 应用ID: 小程序APP ID
APP_ID = "wxcecdaaa94f649478"
# API V3 密钥
API_V3_KEY = "fKRth7y4SR9y8wSwRlXGHlmZSOH40sZe"
# 接入模式: False: 直连商户, True: 服务商
PARTNER_MODE = False
# 代理设置
PROXY = None
# 请求超时时间配置
TIMEOUT = (10, 30)  # 建立连接最大超时时间是10s，读取响应的最大超时时间是30s
# 回调地址，也可以在调用接口的时候覆盖。
NOTIFY_URL = "https://minruiapi.oxdtu.com:2315/mini/wechat_pay/notify"
# 微信支付平台证书缓存目录，初始调试的时候可以设为None，首次使用确保此目录为空目录。
CERT_DIR = './cert/temp_cert'
# 日志记录器，记录web请求和回调细节，便于调试排错。
logging.basicConfig(filename=os.path.join(os.getcwd(), 'payment.log'), level=logging.DEBUG, filemode='a', format='%(asctime)s - %(process)s - %('
                                                                                                                 'levelname)s: %(message)s')
LOGGER = logging.getLogger("demo")
# 初始化
wxpay = WeChatPay(
    wechatpay_type=WeChatPayType.NATIVE,
    mchid=MCH_ID,
    private_key=PRIVATE_KEY,
    cert_serial_no=CERT_SERIAL_NO,
    apiv3_key=API_V3_KEY,
    appid=APP_ID,
    notify_url=NOTIFY_URL,
    cert_dir=CERT_DIR,
    logger=LOGGER,
    partner_mode=PARTNER_MODE,
    proxy=PROXY,
    timeout=TIMEOUT
)


# 创建订单
@mini_orders.route("/mini/orders/add", methods=["POST"])
def add_orders():
    params = json.loads(request.data)
    if "packetsId" in params and "out_trade_no" in params and "openid" in params and "total_fee" in params and "packetsName" in params:
        try:
            # 流量包ID
            packets_id = params["packetsId"]
            packets_name = params["packetsName"]
            # 订单号
            out_trade_no = params["out_trade_no"]
            # 支付用户openid
            openid = params["openid"]
            # 支付金额, 单位: 分 , 实际价格需要乘100
            total_fee = float(params["total_fee"])
            amount = int(total_fee * 100)
            description = '购买流量套餐, 名称为' + packets_name
            payer = {"openid": openid}
            code, message = wxpay.pay(
                description=description,
                out_trade_no=out_trade_no,
                amount={'total': amount},
                pay_type=WeChatPayType.MINIPROG,
                payer=payer
            )
            result = json.loads(message)
            if code in range(200, 300):
                prepay_id = result.get('prepay_id')
                timestamp = str(int(time.time()))
                nonce_str = str(uuid.uuid4()).replace('-', '')
                package = 'prepay_id=' + prepay_id
                sign = wxpay.sign(data=[APP_ID, timestamp, nonce_str, package])
                sign_type = 'RSA'
                wxpay_result = {
                    "appId": APP_ID,
                    "timeStamp": timestamp,
                    "nonceStr": nonce_str,
                    "package": 'prepay_id=%s' % prepay_id,
                    "signType": sign_type,
                    "paySign": sign
                }
                db = SQLManager()
                # 查询相关绑定的数据
                select_sql = ("select tbl_u.id as user_id, tbl_u.phone as user_phone, tbl_d.id as dev_id, tbl_d.imei_code from tbl_user tbl_u inner "
                              "join tbl_devices tbl_d on tbl_d.imei_code=tbl_u.imei_code where tbl_u.is_delete=0 and tbl_u.openid=%s and "
                              "tbl_d.is_delete=0")
                select_result = db.get_one(select_sql, args=[openid])
                if select_result is not None:
                    user_id = select_result["user_id"]
                    user_phone = select_result["user_phone"]
                    dev_id = select_result["dev_id"]
                    imei_code = select_result["imei_code"]
                    try:
                        # 写入数据库
                        insert_sql = (
                            "insert into tbl_orders (order_no, packets_id, user_id, dev_id, imei_code, openid, user_phone, order_amount) values(%s, "
                            "%s, %s, %s, %s, %s, %s, %s)")
                        db.cursor.execute(insert_sql, args=[out_trade_no, packets_id, user_id, dev_id, imei_code, openid, user_phone, total_fee])
                        db.conn.commit()
                    except Exception as err:
                        print("创建订单错误", err)
                        db.conn.rollback()
                        return JsonResponse.fail(msg="error", data=err)
                    finally:
                        db.close()
                    return JsonResponse.success(msg='success', data=wxpay_result)
                else:
                    return JsonResponse.fail(msg="error", data="未知错误")
            else:
                return JsonResponse.fail(msg="error", data=result.get('code'))
        except Exception as err:
            print("下单失败", err)
            return JsonResponse.fail(msg="error", data=err)
    else:
        return JsonResponse.fail(msg="error", data="缺少参数")


# 订单支付成功回调
@mini_orders.route('/mini/wechat_pay/notify', methods=['POST'])
def wechat_pay_notify():
    result = wxpay.callback(request.headers, request.data)
    if result and result.get('event_type') == 'TRANSACTION.SUCCESS':
        resp = result.get('resource')
        print("支付回调结果:", resp)
        app_id = resp.get('appid')
        mch_id = resp.get('mchid')
        out_trade_no = resp.get('out_trade_no')
        transaction_id = resp.get('transaction_id')
        trade_type = resp.get('trade_type')
        trade_state = resp.get('trade_state')
        trade_state_desc = resp.get('trade_state_desc')
        bank_type = resp.get('bank_type')
        attach = resp.get('attach')
        success_time = resp.get('success_time')
        payment_time = datetime.datetime.fromisoformat(str(success_time).replace('+08:00', ''))
        payer = resp.get('payer')
        amount = resp.get('amount').get('total')
        # TODO: 根据返回参数进行必要的业务处理，处理完后返回200或204
        # 成功之后改变对应的订单状态, 增加设备有效时长
        db = SQLManager()
        try:
            # 改变订单状态
            update_orders = "update tbl_orders set trade_no=%s, payment_status=%s, order_status=%s, payment_time=%s where order_no=%s"
            db.cursor.execute(update_orders, args=[transaction_id, 1, 2, payment_time, out_trade_no])
            # 改变设备卡片有效时间
            select_order = ("select tbl_d.imei_code, tbl_d.binding_time, tbl_d.effective_time, tbl_p.lifespan from tbl_devices tbl_d inner join "
                            "tbl_orders tbl_o on tbl_o.imei_code=tbl_d.imei_code inner join tbl_packets tbl_p on tbl_p.id=tbl_o.packets_id where "
                            "tbl_o.order_no=%s and tbl_d.is_delete=0")
            select_res = db.get_one(select_order, args=[out_trade_no])
            # 增加时长: 年
            years_to_add = int(select_res["lifespan"])
            if select_res["effective_time"] is not None and select_res["effective_time"] != "":
                # 在当前的有效期叠加时长
                current_time = datetime.datetime.strptime(str(select_res["effective_time"]), "%Y-%m-%d %H:%M:%S")
            else:
                # 在绑定时间叠加
                current_time = datetime.datetime.strptime(str(select_res["binding_time"]), "%Y-%m-%d %H:%M:%S")
            last_time = current_time + relativedelta(years=years_to_add)
            update_device = "update tbl_devices set effective_time=%s where imei_code=%s"
            db.cursor.execute(update_device, args=[last_time, select_res["imei_code"]])
            db.conn.commit()
        except Exception as err:
            print("订单状态改变失败", err)
            db.conn.rollback()
        finally:
            db.close()
        print("支付成功")
        return JsonResponse.success(msg='success', data="支付成功")

    else:
        print("支付失败")
        return JsonResponse.fail(msg='fail', data="支付失败")


# 获取订单列表
@mini_orders.route("/mini/orders/list", methods=["POST"])
def get_orders_list():
    params = json.loads(request.data)
    if "user_phone" not in params or "imei_code" not in params or "openid" not in params:
        return JsonResponse.fail(msg="error", data="缺少参数")
    else:
        db = SQLManager()
        sql = ("select tbl_o.*, tbl_p.packets_name, tbl_p.unit_price, tbl_p.lifespan from tbl_orders tbl_o inner join tbl_packets tbl_p on "
               "tbl_p.id=tbl_o.packets_id where tbl_o.is_delete=0 and tbl_o.user_phone=%s and tbl_o.imei_code=%s and tbl_o.openid=%s order by "
               "tbl_o.create_time desc")
        user_phone = params["user_phone"]
        imei_code = params["imei_code"]
        openid = params["openid"]
        res_list = db.get_list(sql, args=[user_phone, imei_code, openid])
        return JsonResponse.success(msg='success', data=res_list)
