import json
import time

from flask import Blueprint
from paho.mqtt import client as mqtt
from queue import Queue
from api.config.config import *
from api.config.send_notice import send_notice
import datetime as datetime

mqtt_api = Blueprint('mqtt_api', __name__)

mqtt_client_id = 'GID_TEST1@@@001'
username = "Signature|LTAI5tCQT1JcHvZngW522pu7|post-cn-lmr3yzgso02"
password = "7+DxZ1QagWaL7zS5Ezb54x4YdgE="

# mqtt_client_id = 'GID_TEST1@@@002'
# username = "Signature|LTAI5tCQT1JcHvZngW522pu7|post-cn-lmr3yzgso02"
# password = "f7OYtmC29TlOFF/pjJuG/vaHr4s="


broker_address = 'post-cn-lmr3yzgso02.mqtt.aliyuncs.com'
mqtt_port = 1883  # 默认端口


# mqtt封装
class MQTTClient:
    def __init__(self, host, port, qos, client_id, heartbeat, user, pwd):
        self.host = host
        self.port = port
        self.qos = qos
        self.heartbeat = heartbeat
        self.clientID = client_id
        self.username = user
        self.password = pwd
        self.queue = Queue()
        self.mqtt_client = mqtt.Client(mqtt.CallbackAPIVersion.VERSION1, self.clientID, clean_session=False)
        self.mqtt_client.on_connect = self.on_connect
        self.mqtt_client.on_message = self.usr_on_message
        self.mqtt_client.on_publish = self.usr_on_publish
        self.mqtt_client.on_disconnect = self.on_disconnect
        self.mqtt_client.username_pw_set(self.username, self.password)

    # 收到订阅的数据
    def usr_on_message(self, user, data, msg):
        # 将数据保存到数据库
        print("收到数据主题为:", msg.topic)
        print("收到原数据为:", msg.payload)
        if msg is not None:
            imei_code = msg.topic.split("/")[2]
            if msg.topic.find('GPS') != -1:
                # 收到gps相关的数据
                gps_payload_data = str(msg.payload.decode("utf-8"))
                location_json = json.loads(gps_payload_data)
                print("收到GPS数据为:", location_json)
                db = SQLManager()
                try:
                    location_str = str(location_json["E"]) + "," + str(location_json["N"])
                    update_param = ("update tbl_parameter tbl_p inner join tbl_devices tbl_d on tbl_p.dev_id = tbl_d.id set tbl_p.param_value=%s "
                                    "where tbl_p.is_delete=0 and tbl_d.is_delete=0 and tbl_p.param_code=%s and tbl_d.imei_code=%s")
                    db.cursor.execute(update_param, args=[location_str, "location", imei_code])

                    # 判断是已经存在对应的ICCID
                    if "I" in location_json and location_json["I"] is not None and location_json["I"] != "":
                        check_exist = "select iccid from tbl_devices where imei_code=%s"
                        check_res = db.get_one(check_exist, args=[imei_code])
                        if check_res is None or check_res["iccid"] == "":
                            iccid = location_json["I"]
                            update_devices = "update tbl_devices set iccid=%s where imei_code=%s"
                            db.cursor.execute(update_devices, args=[iccid, imei_code])
                    db.conn.commit()
                except Exception as err:
                    db.conn.rollback()
                    print(err)
                finally:
                    db.close()
            elif msg.topic.find('STATUS') != -1:
                # 接收设备状态相关的数据
                # 区分全部状态, 警戒<A501>, 解锁<A502>, 静音<A503>, 启动<A504>, 尾门<A505>, 车窗<A506>, 电量<A507>, 震动报警<A508>, 钥匙报警<A509>
                temp = msg.payload.hex()
                target_sign = temp[0:4]
                base_16 = int(temp[4:8], 16)
                db = SQLManager()
                try:
                    if target_sign.upper() == "A501":
                        # 警戒
                        update_param = (
                            "update tbl_parameter tbl_p inner join tbl_devices tbl_d on tbl_p.dev_id = tbl_d.id set tbl_p.param_value=%s where "
                            "tbl_p.is_delete=0 and tbl_d.is_delete=0 and tbl_p.param_code=%s and tbl_d.imei_code=%s")
                        db.cursor.execute(update_param, args=[1, "guard_status", imei_code])
                    elif target_sign.upper() == "A502":
                        # 解锁
                        update_param = (
                            "update tbl_parameter tbl_p inner join tbl_devices tbl_d on tbl_p.dev_id = tbl_d.id set tbl_p.param_value=%s where "
                            "tbl_p.is_delete=0 and tbl_d.is_delete=0 and tbl_p.param_code=%s and tbl_d.imei_code=%s")
                        db.cursor.execute(update_param, args=[1, "lock_status", imei_code])
                    elif target_sign.upper() == "A503":
                        # 静音
                        update_param = (
                            "update tbl_parameter tbl_p inner join tbl_devices tbl_d on tbl_p.dev_id = tbl_d.id set tbl_p.param_value=%s where "
                            "tbl_p.is_delete=0 and tbl_d.is_delete=0 and tbl_p.param_code=%s and tbl_d.imei_code=%s")
                        db.cursor.execute(update_param, args=[1, "mute_status", imei_code])
                    elif target_sign.upper() == "A504":
                        # 启动
                        update_param = (
                            "update tbl_parameter tbl_p inner join tbl_devices tbl_d on tbl_p.dev_id = tbl_d.id set tbl_p.param_value=%s where "
                            "tbl_p.is_delete=0 and tbl_d.is_delete=0 and tbl_p.param_code=%s and tbl_d.imei_code=%s")
                        db.cursor.execute(update_param, args=[1, "car_status", imei_code])
                    elif target_sign.upper() == "A505":
                        # 尾门
                        update_param = (
                            "update tbl_parameter tbl_p inner join tbl_devices tbl_d on tbl_p.dev_id = tbl_d.id set tbl_p.param_value=%s where "
                            "tbl_p.is_delete=0 and tbl_d.is_delete=0 and tbl_p.param_code=%s and tbl_d.imei_code=%s")
                        db.cursor.execute(update_param, args=[1, "tailgate_status", imei_code])
                    elif target_sign.upper() == "A506":
                        # 车窗
                        update_param = (
                            "update tbl_parameter tbl_p inner join tbl_devices tbl_d on tbl_p.dev_id = tbl_d.id set tbl_p.param_value=%s where "
                            "tbl_p.is_delete=0 and tbl_d.is_delete=0 and tbl_p.param_code=%s and tbl_d.imei_code=%s")
                        db.cursor.execute(update_param, args=[1, "window_status", imei_code])
                    elif target_sign.upper() == "A507":
                        # 电量
                        update_param = (
                            "update tbl_parameter tbl_p inner join tbl_devices tbl_d on tbl_p.dev_id = tbl_d.id set tbl_p.param_value=%s where "
                            "tbl_p.is_delete=0 and tbl_d.is_delete=0 and tbl_p.param_code=%s and tbl_d.imei_code=%s")
                        db.cursor.execute(update_param, args=[base_16, "endurance_status", imei_code])
                    elif target_sign.upper() == "A508":
                        # 需要发送短信通知车主
                        # 震动报警
                        update_param = (
                            "update tbl_parameter tbl_p inner join tbl_devices tbl_d on tbl_p.dev_id = tbl_d.id set tbl_p.param_value=%s where "
                            "tbl_p.is_delete=0 and tbl_d.is_delete=0 and tbl_p.param_code=%s and tbl_d.imei_code=%s")
                        db.cursor.execute(update_param, args=[1, "vibration_status", imei_code])
                        # 开始发送短信
                        current_time = datetime.datetime.strftime(datetime.datetime.now(), '%Y-%m-%d %H:%M:%S')
                        select_phone = "select phone from tbl_user where imei_code=%s and is_delete=0"
                        select_res = db.get_one(select_phone, args=[imei_code])
                        if select_res is not None and select_res["phone"] is not None and select_res["phone"] != "":
                            send_notice(select_res["phone"], current_time)
                    elif target_sign.upper() == "A509":
                        # 需要发送短信通知车主
                        update_param = (
                            "update tbl_parameter tbl_p inner join tbl_devices tbl_d on tbl_p.dev_id = tbl_d.id set tbl_p.param_value=%s where "
                            "tbl_p.is_delete=0 and tbl_d.is_delete=0 and tbl_p.param_code=%s and tbl_d.imei_code=%s")
                        db.cursor.execute(update_param, args=[1, "key_status", imei_code])
                        # 开始发送短信
                        current_time = datetime.datetime.strftime(datetime.datetime.now(), '%Y-%m-%d %H:%M:%S')
                        select_phone = "select phone from tbl_user where imei_code=%s and is_delete=0"
                        select_res = db.get_one(select_phone, args=[imei_code])
                        if select_res is not None and select_res["phone"] is not None and select_res["phone"] != "":
                            send_notice(select_res["phone"], current_time)
                    else:
                        print("转换后", temp)
                        # 1111111 -> 右边第一个对应 bit 0
                        # bit 0   =1 警戒状态  			=0 非警戒状态
                        # bit 1   =1 解锁状态  			=0 非解锁状态
                        # bit 2   =1 启动状态  			=0 非启动状态
                        # bit 3   =1 尾门打开状态  		=0 尾门关闭状态
                        # bit 4   =1 车窗打开状态  		=0 车窗关闭状态
                        # bit 5   =1  PKE自动感应状态 	=0 PKE门把手触发状态
                        # bit 6   =1 刹车状态 	 		=0 非刹车状态
                        # bit 7   =1 静音状态             =0 非静音状态
                        #
                        base_2 = str(bin(base_16)[2:])
                        chars = [char for char in base_2]
                        # guard_status: 警戒/非警戒
                        # lock_status: 解锁/非解锁
                        # car_status: 启动/熄火
                        # tailgate_status: 尾门
                        # window_status: 车窗
                        # pke_status: PKE 自动感应 / 门把手触发
                        # brake_status: 刹车
                        status_arr = ["guard_status", "lock_status", "car_status", "tailgate_status", "window_status", "pke_status", "brake_status",
                                      "mute_status"]
                        chars.reverse()
                        if len(chars) < 8:
                            for i in range(8 - len(chars)):
                                chars.append("0")
                        for i in range(len(chars)):
                            if i < len(chars):
                                update_param = (
                                    "update tbl_parameter tbl_p inner join tbl_devices tbl_d on tbl_p.dev_id = tbl_d.id set tbl_p.param_value=%s where "
                                    "tbl_p.is_delete=0 and tbl_d.is_delete=0 and tbl_p.param_code=%s and tbl_d.imei_code=%s")
                                db.cursor.execute(update_param, args=[chars[i], status_arr[i], imei_code])

                    db.conn.commit()
                except Exception as err:
                    db.conn.rollback()
                    print(err)
                finally:
                    db.close()
        self.queue.put(msg)

    # 订阅
    def usr_subscribe(self, topic):
        self.mqtt_client.subscribe(topic, self.qos)
        print('订阅主题为: %s' % topic)

    # 取消订阅
    def usr_unsubscribe(self, topic):
        self.mqtt_client.unsubscribe(topic)
        print('unsubscribe %s' % topic)

    def receive_msg(self, timeout=None):
        print('waiting for message.')
        if timeout is None:
            timeout = self.heartbeat
        return self.queue.get(timeout=timeout)

    # 发布主题
    def usr_publish(self, topic, payload, qos, retain=False):
        # retain=False
        print("发布主题==%s, 数据==%s" % (topic, payload))
        result = self.mqtt_client.publish(topic, payload, qos, retain)
        return result

    # 数据发布成功回调
    def usr_on_publish(self, client, userdata, mid):
        print("数据发布成功", mid)

    # 连接mqtt
    def connect(self):
        self.mqtt_client.connect(self.host, self.port, self.heartbeat, )
        self.mqtt_client.reconnect_delay_set(max_delay=10)
        self.mqtt_client.loop_start()

    # mqtt连接成功后回调
    def on_connect(self, client, userdata, flags, rc):
        print("mqtt连接成功, clientID=", self.clientID)
        if rc == 0:
            self.mqtt_client.subscribe("GZMR/#")

    # 断开mqtt
    def on_disconnect(self, client, userdata, rc):
        print("断开连接")
        if rc != 0:
            print("重新连接")
            self.mqtt_client.reconnect()
