import json
from flask import request, Blueprint
from api.config.json_response import JsonResponse
from api.config.config import *

pc_orders = Blueprint('pc_orders', __name__)


# 套餐列表列表--分页
@pc_orders.route("/orders/list", methods=["POST"])
def get_orders_list():
    params = json.loads(request.data)
    db = SQLManager()
    sql = "select tbl_o.*, tbl_p.packets_name from tbl_orders tbl_o inner join tbl_packets tbl_p on tbl_p.id=tbl_o.packets_id where tbl_o.is_delete=0"
    sql_count = ("select count(tbl_o.id) as total from tbl_orders tbl_o inner join tbl_packets tbl_p on tbl_p.id=tbl_o.packets_id where "
                 "tbl_o.is_delete=0")

    if 'order_no' in params and params['order_no'] is not None and params['order_no'] != "":
        imei_code = str(params['imei_code'])
        sql += " and tbl_o.order_no like '%" + imei_code + "%'"
        sql_count += " and tbl_o.order_no like '%" + imei_code + "%'"

    if 'imei_code' in params and params['imei_code'] is not None and params['imei_code'] != "":
        imei_code = str(params['imei_code'])
        sql += " and tbl_o.imei_code like '%" + imei_code + "%'"
        sql_count += " and tbl_o.imei_code like '%" + imei_code + "%'"

    if 'payment_status' in params and params['payment_status'] is not None and params['payment_status'] != "":
        payment_status = str(params['payment_status'])
        sql += " and tbl_o.payment_status=" + payment_status
        sql_count += " and tbl_o.payment_status=" + payment_status

    if 'create_time' in params and params['create_time'] is not None and params['create_time'] != "":
        sql += " and tbl_o.create_time >= '" + str(params['create_time'][0]) + "' and  tbl_o.create_time <= '" + str(params['create_time'][1]) + "'"
        sql_count += " and tbl_o.create_time >= '" + str(params['create_time'][0]) + "' and tbl_o.create_time <= '" + str(
            params['create_time'][1]) + "'"

    count_res = db.get_list(sql_count)
    total = count_res[0]['total']
    limit = params['pageSize']
    current = params['pageNum']
    limit_min = (int(current) - 1) * int(limit)
    sql += ' limit ' + str(limit_min) + ',' + str(limit)
    res_list = db.get_list(sql)
    res = {
        "list": res_list,
        "pageNum": current,
        "pageSize": limit,
        "total": total
    }
    return JsonResponse.success(msg='success', data=res)


# 返回付款状态
@pc_orders.route('/orders/payment/type', methods=['POST'])
def get_payment_type():
    res_list = [
        {
            "paymentLabel": "已付款",
            "paymentValue": 1
        },
        {
            "paymentLabel": "未付款",
            "paymentValue": 0
        }
    ]
    return JsonResponse.success(msg='success', data=res_list)