import datetime
import os
import unicodedata
from flask import request, Blueprint
from werkzeug.utils import secure_filename
from api.config.json_response import JsonResponse
from api.config.config import *
pc_upload = Blueprint('pc_upload', __name__)


# 处理上传文件为中文名
def safe_filename(filename):
    filename = unicodedata.normalize('NFKD', filename).encode('IDNA')
    filename = filename.decode('utf-8')
    return ''.join(c for c in filename if c.isprintable())


# 上传小程序端显示的公司logo和汽车展示图
@pc_upload.route("/upload/img", methods=["POST"])
def upload_img():
    params = request.form
    company_id = params.get("company_id")
    company_banner = params.get("company_banner")
    company_logo = params.get("company_logo")
    sign = params.get("sign")
    if company_id is None or company_banner is None or company_logo is None or sign is None:
        return JsonResponse.fail(msg='error', data="缺少参数")
    if 'file' not in request.files:
        return JsonResponse.fail(msg='error', data="上传失败,文件不存在")
    file = request.files['file']
    if file.filename == '':
        return JsonResponse.fail(msg='error', data="上传失败,文件不存在")
    if file:
        try:
            filename = secure_filename(safe_filename(file.filename))
            folder_path = "../background/files/" + str(company_id)
            # 检查文件夹是否存在
            if not os.path.exists(folder_path):
                os.makedirs(folder_path)
            # 保存到目录文件夹
            new_name = str(int(datetime.datetime.now().timestamp() * 1000)) + "." + filename.split(".")[1]
            file.save(os.path.join(folder_path, new_name))

            # 更新数据库数据
            db = SQLManager()
            # old_file_name = ""
            if sign == "2" or sign == 2:
                update_sql = "update tbl_company set company_banner=%s where id=%s and is_delete=0"
                old_file_name = company_banner
                db.cursor.execute(update_sql, args=[new_name, company_id])
            else:
                update_sql = "update tbl_company set company_logo=%s where id=%s and is_delete=0"
                old_file_name = company_logo
                db.cursor.execute(update_sql, args=[new_name, company_id])
            # 删除旧的文件
            old_file_path = folder_path + "/" + old_file_name
            if os.path.isfile(old_file_path):
                os.remove(old_file_path)
            # 最终显示图片的地址为 https://mradmin.oxdtu.com/files/1/20240517175504.png
            # file_url = "https://mradmin.oxdtu.com/files/" + str(company_id) + "/" + new_name
            db.conn.commit()
            return JsonResponse.success(msg='success', data="上传成功")
        except Exception as error:
            db.conn.rollback()
            print("上传失败", error)
            return JsonResponse.fail(msg='error', data=error)
        finally:
            db.close()
