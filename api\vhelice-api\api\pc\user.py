import json
from flask import request, Blueprint
from api.config.json_response import JsonResponse
from api.config.config import *
import hashlib
import datetime as datetime
from api.config.util_jwt import Util_jwt

pc_user = Blueprint('pc_user', __name__)
util_jwt = Util_jwt()


# 获取登录用户数据
@pc_user.route("/user/info", methods=["post"])
def get_user_info():
    data = json.loads(request.data)
    m = hashlib.md5()
    m.update(data['password'].encode('utf-8'))
    password = m.hexdigest()
    user_name = data['username']
    sql = ("select u.id,u.user_name,u.link_name,u.phone,u.avatar,u.role_id,u.create_time,u.update_time,r.description,r.role_name from tbl_user u inner join "
           "sys_role r on r.id=u.role_id where u.user_name=%s and u.password=%s limit 1")
    db = SQLManager()
    res = db.get_one(sql, args=[user_name, password])
    db.close()
    return JsonResponse.success(msg='success', data=res)


# 修改密码
@pc_user.route("/user/password/update", methods=["post"])
def update_user_password():
    data = json.loads(request.data)
    db = SQLManager()
    try:
        old = hashlib.md5()
        new = hashlib.md5()
        old.update(data['oldPassword'].encode('utf-8'))
        new.update(data['newPassword'].encode('utf-8'))
        old_password = old.hexdigest()
        new_password = new.hexdigest()
        # 检查旧密码是否正确
        check_user = "select id from tbl_user where id=%s and password=%s"
        check_res = db.get_one(check_user, args=[data['id'], old_password])
        if check_res is not None:
            update_sql = "update tbl_user set password=%s where id=%s"
            db.modify(update_sql, args=[new_password, data['id']])
        else:
            return JsonResponse.fail(msg="error", data="旧密码不正确")
    except Exception as err:
        print("密码修改错误", err)
        db.conn.rollback()
        return JsonResponse.fail(msg='error', data=err)
    finally:
        db.close()
    return JsonResponse.success(msg="success", data="修改成功")


# 添加用户
@pc_user.route("/user/add", methods=["POST"])
def add_user():
    data = json.loads(request.data)
    db = SQLManager()
    check_repeat = db.get_list('select id from tbl_user where user_name=%s and is_delete=0', args=[data['user_name']])
    if len(check_repeat) >= 1:
        return JsonResponse.fail(msg='error', data='用户名称已存在')
    else:
        try:
            m = hashlib.md5()
            m.update(data['password'].encode('utf-8'))
            password = m.hexdigest()
            # 添加数据到用户表
            insert_user = 'insert into tbl_user (user_name, password, phone, link_name, role_id) values(%s,%s,%s,%s,%s)'
            db.cursor.execute(insert_user, args=[data['user_name'], password, data['phone'], data['link_name'], data['role_id']])
            user_id = db.conn.insert_id()
            # 添加数据到角色和用户关联表
            insert_role = 'insert into tbl_user_role (user_id, role_id) values(%s,%s)'
            db.cursor.execute(insert_role, args=[user_id, data['role_id']])
            db.conn.commit()
        except Exception as err:
            print('添加用户失败', err)
            db.conn.rollback()
            return JsonResponse.fail(msg='error', data=err)
        finally:
            db.close()
        return JsonResponse.success(msg='success', data='添加成功')


# 更新用户
@pc_user.route("/user/update", methods=["POST"])
def update_user():
    data = json.loads(request.data)
    db = SQLManager()
    check_repeat = db.get_list('select id from tbl_user where user_name=%s and is_delete=0 and id!=%s', args=[data['user_name'], data['id']])
    if len(check_repeat) >= 1:
        return JsonResponse.fail(msg='error', data='用户名称已存在')
    else:
        try:
            update_user_sql = 'update tbl_user set user_name=%s, phone=%s, link_name=%s, role_id=%s where id=%s'
            db.cursor.execute(update_user_sql, args=[data['user_name'], data['phone'], data['link_name'], data['role_id'], data['id']])
            # 判断关联的用户角色是否一致 , 不一致的话先将关联的数据删除后再重新添加关联
            check_user_role = db.get_one('select * from tbl_user_role where user_id=%s and is_delete=0', args=[data['id']])
            if check_user_role['role_id'] != data['role_id']:
                update_user_role = 'update tbl_user_role set is_delete=1 where id=%s'
                db.cursor.execute(update_user_role, args=[check_user_role['id']])
                insert_user_role = 'insert into tbl_user_role (user_id, role_id) values(%s, %s)'
                db.cursor.execute(insert_user_role, args=[data['id'], data['role_id']])
            db.conn.commit()
        except Exception as err:
            print('更新用户失败', err)
            db.conn.rollback()
            return JsonResponse.fail(msg='error', data=err)
        finally:
            db.close()
        return JsonResponse.success(msg='success', data='更新成功')


# 删除用户
@pc_user.route("/user/delete", methods=["POST"])
def delete_user():
    data = json.loads(request.data)
    db = SQLManager()
    try:
        # 删除用户表
        delete_user_sql = 'update tbl_user set is_delete=1 where id=%s'
        db.cursor.execute(delete_user_sql, args=[data['id']])
        # 删除用户角色关联表
        delete_user_role_sql = 'update tbl_user_role set is_delete=1 where user_id=%s'
        db.cursor.execute(delete_user_role_sql, args=[data['id']])
        db.conn.commit()
    except Exception as err:
        print('删除用户失败', err)
        db.conn.rollback()
        return JsonResponse.fail(msg='error', data=err)
    finally:
        db.close()
    return JsonResponse.success(msg='success', data='删除成功')


# 用户列表--分页
@pc_user.route("/user/list/page", methods=["POST"])
def get_user_list_page():
    data = json.loads(request.data)
    db = SQLManager()
    sql = "select u.*,r.description,r.role_name from tbl_user u inner join sys_role r on r.id=u.role_id where u.is_delete=0 "
    sql_count = "select count(u.id) as total from tbl_user u inner join sys_role r on r.id=u.role_id where u.is_delete=0 "
    countRes = db.get_list(sql_count)
    total = countRes[0]['total']
    limit = data['pageSize']
    current = data['pageNum']
    min = (int(current) - 1) * int(limit)
    sql += ' limit ' + str(min) + ',' + str(limit)
    resList = db.get_list(sql)
    res = {
        "list": resList,
        "pageNum": current,
        "pageSize": limit,
        "total": total
    }
    return JsonResponse.success(msg='success', data=res)


# 导出用户列表
@pc_user.route("/user/export", methods=["POST"])
def export_user():
    db = SQLManager()
    data = json.loads(request.data)
    limit = data['pageSize']
    current = data['pageNum']
    min = (int(current) - 1) * int(limit)
    selectSql = "select u.*,r.description,r.role_name from tbl_user u inner join sys_role r on r.id=u.role_id where u.is_delete=0 "
    selectSql += ' limit ' + str(min) + ',' + str(limit)
    resList = db.get_list(selectSql)
    tempList = [
        ['用户名称', '昵称', '联系电话', '角色名称', '创建时间']
    ]
    for item in resList:
        arr = [
            item['user_name'],
            item['link_name'],
            item['phone'],
            item['role_name'],
            datetime.datetime.strftime(item['create_time'], '%Y-%m-%d %H:%M:%S')
        ]
        tempList.append(arr)
    db.close()
    return JsonResponse.success(msg="success", data=tempList)
