import json
from flask import request, Blueprint
from api.config.json_response import JsonResponse
from api.config.config import *

mini_packets = Blueprint('mini_packets', __name__)


# 套餐列表列表--分页
@mini_packets.route("/mini/packets/list", methods=["GET"])
def get_packet_list():
    db = SQLManager()
    sql = "select * from tbl_packets where is_delete=0 "
    res_list = db.get_list(sql)
    return JsonResponse.success(msg='success', data=res_list)