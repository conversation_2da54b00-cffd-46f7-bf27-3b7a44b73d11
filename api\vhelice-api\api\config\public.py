import random


# 生成一个指定长度的随机字符串
def generate_random_str(random_length=10):
    random_str = ''
    base_str = 'ABCDEFGHIGKLMNOPQRSTUVWXYZabcdefghigklmnopqrstuvwxyz0123456789'
    length = len(base_str) - 1
    for i in range(random_length):
        random_str += base_str[random.randint(0, length)]
    return random_str


# 生成随机数
def generate_random_number(random_length=6):
    number = ""
    for _ in range(random_length):
        number += str(random.randint(0, 9))
    return number
