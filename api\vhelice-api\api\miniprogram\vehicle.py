import json
import requests
from flask import request, Blueprint
from api.config.config import *
from api.config.json_response import JsonResponse
from api.miniprogram.gps import wgs84_to_gcj02


mini_vehicle = Blueprint('mini_vehicle', __name__)


# 获取绑定设备的数据
@mini_vehicle.route("/mini/vehicle/device", methods=["POST"])
def get_vehicle_device():
    params = json.loads(request.data)
    if "imei_code" in params and params["imei_code"] != "" and params["imei_code"] is not None:
        imei_code = params["imei_code"]
        select_sql = "select * from tbl_devices where imei_code=%s and is_delete=0"
        db = SQLManager()
        select_response = db.get_one(select_sql, args=[imei_code])
        if select_response is not None:
            return JsonResponse.success(msg='success', data=select_response)
        else:
            return JsonResponse.success(msg='error', data="数据不存在")
    else:
        return JsonResponse.fail(msg='error', data="缺少参数")


# 获取设备归属商家的数据
@mini_vehicle.route("/mini/vehicle/company", methods=["POST"])
def get_vehicle_company():
    params = json.loads(request.data)
    if "imei_code" in params and params["imei_code"] != "" and params["imei_code"] is not None:
        imei_code = params["imei_code"]
        select_sql = ("select tbl_c.* from tbl_company tbl_c inner join tbl_devices tbl_d on tbl_c.id = tbl_d.company_id where tbl_d.imei_code=%s "
                      "and tbl_c.is_delete=0 and tbl_d.is_delete=0")
        db = SQLManager()
        select_response = db.get_one(select_sql, args=[imei_code])
        if select_response is not None:
            return JsonResponse.success(msg='success', data=select_response)
        else:
            return JsonResponse.success(msg='error', data="数据不存在")
    else:
        return JsonResponse.fail(msg='error', data="缺少参数")


# 获取车辆所有状态数据
@mini_vehicle.route("/mini/vehicle/info", methods=["POST"])
def get_vehicle_info():
    params = json.loads(request.data)
    if "imei_code" in params and params["imei_code"] != "" and params["imei_code"] is not None:
        imei_code = params["imei_code"]
        select_sql = ("select tbl_p.* from tbl_parameter tbl_p inner join tbl_devices tbl_d on tbl_p.dev_id = tbl_d.id where tbl_d.imei_code=%s and "
                      "tbl_p.is_delete=0 and tbl_d.is_delete=0")
        db = SQLManager()
        select_response = db.get_list(select_sql, args=[imei_code])
        if select_response is not None:
            return JsonResponse.success(msg='success', data=select_response)
        else:
            return JsonResponse.success(msg='error', data="数据不存在")
    else:
        return JsonResponse.fail(msg='error', data="缺少参数")


# 获取单个参数的数据
@mini_vehicle.route("/mini/vehicle/target", methods=["POST"])
def get_vehicle_target():
    params = json.loads(request.data)
    if "imei_code" in params and params["imei_code"] != "" and params["imei_code"] is not None:
        imei_code = params["imei_code"]
        param_name = params["param_name"]
        select_sql = ("select tbl_p.* from tbl_parameter tbl_p inner join tbl_devices tbl_d on tbl_p.dev_id = tbl_d.id where tbl_d.imei_code=%s and "
                      "tbl_p.is_delete=0 and tbl_d.is_delete=0 and tbl_p.param_code=%s")

        db = SQLManager()
        select_response = db.get_list(select_sql, args=[imei_code, param_name])
        if select_response is not None:
            return JsonResponse.success(msg='success', data=select_response)
        else:
            return JsonResponse.success(msg='success', data="数据不存在")
    else:
        return JsonResponse.fail(msg='error', data="缺少参数")


# 获取车辆续航数据
@mini_vehicle.route("/mini/vehicle/endurance", methods=["POST"])
def get_vehicle_endurance():
    params = json.loads(request.data)
    if "imei_code" in params and params["imei_code"] != "" and params["imei_code"] is not None:
        imei_code = params["imei_code"]
        select_sql = ("select tbl_p.* from tbl_parameter tbl_p inner join tbl_devices tbl_d on tbl_p.dev_id = tbl_d.id where tbl_d.imei_code=%s and "
                      "tbl_p.is_delete=0 and tbl_d.is_delete=0 and tbl_p.param_code='endurance_status'")

        db = SQLManager()
        select_response = db.get_list(select_sql, args=[imei_code])
        if select_response is not None:
            return JsonResponse.success(msg='success', data=select_response)
        else:
            return JsonResponse.success(msg='success', data="数据不存在")
    else:
        return JsonResponse.fail(msg='error', data="缺少参数")


# 获取车辆当前位置
@mini_vehicle.route("/mini/vehicle/location", methods=["POST"])
def get_vehicle_location():
    params = json.loads(request.data)
    # 设备主机ID
    if "imei_code" in params and params["imei_code"] != "" and params["imei_code"] is not None:
        imei_code = params["imei_code"]
        db = SQLManager()
        select_sql = ("select tbl_p.* from tbl_parameter tbl_p inner join tbl_devices tbl_d on tbl_p.dev_id = tbl_d.id where tbl_d.imei_code=%s and "
                      "tbl_p.is_delete=0 and tbl_d.is_delete=0 and tbl_p.param_code='location'")
        select_response = db.get_one(select_sql, args=[imei_code])
        if select_response is not None and select_response["param_value"] is not None:
            # 获取到位置后，将坐标信息转换成GCJ02坐标系
            # 将获取到的经纬度发送到高德, 获取对应的详细地址信息
            wgs84_location = select_response["param_value"].split(",")
            gcj02_location = wgs84_to_gcj02(float(wgs84_location[0]), float(wgs84_location[1]))
            location_str = str(gcj02_location[0]) + "," + str(gcj02_location[1])
            key = "1dd00d794b9a004436eb082cd259fc56"
            # # https://restapi.amap.com/v3/geocode/regeo?parameters
            url = "https://restapi.amap.com/v3/geocode/regeo?key=" + key + "&location=" + location_str
            response = requests.get(url)
            res = {
                "response": response.json(),
                "targetLatitude": gcj02_location[1],
                "targetLongitude": gcj02_location[0]
            }

            return JsonResponse.success(msg='success', data=res)
        else:
            return JsonResponse.success(msg='error', data="数据不存在")
    else:
        return JsonResponse.fail(msg='error', data="缺少参数")

